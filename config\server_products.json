{"server_types": {"alicloud": {"name": "阿里云IoT平台", "description": "阿里云物联网平台", "broker_type": "ALIBABA_CLOUD", "default_config": {"port": 1883, "broker_host": "", "username": "", "password": ""}}, "emqx": {"name": "EMQX自建服务器", "description": "EMQX自建MQTT服务器", "broker_type": "EMQX", "default_config": {"port": 1883, "broker_host": "mqtt01.yunpusher.com", "username": "kafang@", "password": "kafang@_2025"}}}, "products": {"alicloud": [{"id": "ali_default", "name": "阿里云默认产品", "product_key": "hs7eigK8Xvl", "server_type": "alicloud", "description": "阿里云IoT平台默认产品", "device_secret": "e50099fbc8ac924105ace0815d31b7a4"}], "emqx": [{"id": "emqx_default", "name": "EMQX默认产品", "product_key": "wxd48e69e833621cfd", "server_type": "emqx", "description": "EMQX服务器默认产品"}, {"id": "emqx_product_2", "name": "EMQX产品2", "product_key": "wx227137014f90cf30", "server_type": "emqx", "description": "EMQX服务器产品2"}]}, "migration_rules": {"alicloud_to_emqx": {"source_server": "alicloud", "target_server": "emqx", "description": "从阿里云迁移到EMQX服务器", "default_target_product": "emqx_default"}, "emqx_to_alicloud": {"source_server": "emqx", "target_server": "alicloud", "description": "从EMQX迁移到阿里云服务器", "default_target_product": "ali_default"}, "emqx_product_change": {"source_server": "emqx", "target_server": "emqx", "description": "EMQX服务器内部产品变更", "default_target_product": "emqx_product_2"}}}