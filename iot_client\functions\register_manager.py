import os
import logging
from datetime import datetime
from iot_client.bin_block.protocol_constants import *
from iot_client.bin_block.bin_block import BinBlock
from iot_client.iot_client import IoTClient
from iot_client.bin_block.reg_addr import RegAddr
from iot_client.platform.platform_type import PlatformType
from iot_client.platform.ali_mqtt_client import AmqpConfig
from iot_client.platform.emqx_mqtt_client import EMQXConfig
from iot_client.bin_block.bin_block import BinBlockMQTTBrokerInfo_t

# 寄存器操作类
class RegisterManager:
    """寄存器操作管理类"""

    def __init__(self, client: IoTClient, topic, logger):
        """
        初始化寄存器管理器

        Args:
            client: IoT客户端
            topic: 主题全名
            logger: 日志记录器
        """
        self.client = client
        self.topic = topic
        self.logger = logger
        self.platform = PlatformType.EMQX if topic.startswith("/wx") else PlatformType.ALIBABA_CLOUD

    def read_registers(self, devid, reg_addr, reg_num, timeout=10):
        """
        读取寄存器

        Args:
            devid: 设备ID
            reg_addr: 寄存器起始地址
            reg_num: 要读取的寄存器数量
            timeout: 超时时间（秒）

        Returns:
            Dict: 包含寄存器值的字典，如果读取失败则返回None
        """
        try:
            # 编码读取命令
            bin_block = BinBlock.encode_read(devid, reg_addr, reg_num)
            msg = self.client.request_syc(self.topic, bin_block, self.platform, timeout=timeout)

            if msg:
                # self.logger.info(f"读取寄存器成功: {msg}")
                return msg
            else:
                self.logger.error("读取寄存器失败: 未收到响应")
                return None
        except Exception as e:
            self.logger.error(f"读取寄存器异常: {e}")
            return None

    def cmd_request_debug_info_query(self, devid, req_type, timeout=10):
        """
        读取寄存器

        Args:
            devid: 设备ID
            req_type: 请求类型
            timeout: 超时时间（秒）

        Returns:
            Dict: 包含寄存器值的字典，如果读取失败则返回None
        """
        try:
            # 编码读取命令
            bin_block = BinBlock.encode_debug_info_query(devid, req_type)
            msg = self.client.request_syc(self.topic, bin_block, self.platform, timeout=timeout)

            if msg:
                # self.logger.info(f"读取寄存器成功: {msg}")
                return msg
            else:
                self.logger.error("查询设备调试信息失败: 未收到响应")
                return None
        except Exception as e:
            self.logger.error(f"查询设备调试信息失败异常: {e}")
            return None

    def cmd_sim_card_info_query(self, devid, timeout=10):
        return self.cmd_request_debug_info_query(devid, 2, timeout)

    def print_register_values(self, msg, start_reg_addr):
        """
        打印寄存器值

        Args:
            msg: 包含寄存器值的消息
            start_reg_addr: 起始寄存器地址
        """
        if not msg or "parsed_data" not in msg or "register_value" not in msg["parsed_data"]:
            self.logger.error("无效的寄存器数据")
            return

        register_value = msg["parsed_data"]["register_value"]

        # 打印每个寄存器的值
        for i in range(len(register_value)):
            reg_addr = start_reg_addr + i
            reg_name = RegAddr.get_reg_name(reg_addr)
            reg_value = register_value[i]
            self.logger.info(f"{reg_name}: {reg_value}")

        # 提取CSQ值（如果存在）
        if register_value and start_reg_addr <= RegAddr.REG_CSQ < start_reg_addr + len(register_value):
            csq_index = RegAddr.REG_CSQ - start_reg_addr
            csq = (register_value[csq_index] >> 8) & 0xFF
            self.logger.info(f"CSQ: {csq}")

    def write_register(self, devid, reg_addr, reg_value, timeout=10):
        """
        写入寄存器

        Args:
            devid: 设备ID
            reg_addr: 寄存器地址
            reg_value: 寄存器值列表
            timeout: 超时时间（秒）

        Returns:
            bool: 写入是否成功
        """
        try:
            # 编码写入命令
            bin_block = BinBlock.encode_set(devid, reg_addr, reg_value)
            msg = self.client.request_syc(self.topic, bin_block, self.platform, timeout=timeout)

            if msg:
                self.logger.info(f"写入寄存器成功: {msg}")
                return True
            else:
                self.logger.error("写入寄存器失败: 未收到响应")
                return False
        except Exception as e:
            self.logger.error(f"写入寄存器异常: {e}")
            return False

    def cmd_mqtt_broker_info_update(self, devid, broker_info: BinBlockMQTTBrokerInfo_t, restart: bool, timeout=10):
        """
        读取寄存器

        Args:
            devid: 设备ID
            broker_info: MQTT Broker 信息
            timeout: 超时时间（秒）

        Returns:
            Dict: 包含寄存器值的字典，如果读取失败则返回None
        """
        try:
            # 编码读取命令
            bin_block = BinBlock.encode_mqtt_broker_info_update(devid, broker_info, restart)
            msg = self.client.request_syc(self.topic, bin_block, self.platform, timeout=timeout)

            if msg:
                # self.logger.info(f"读取寄存器成功: {msg}")
                return msg
            else:
                self.logger.error("查询设备调试信息失败: 未收到响应")
                return None
        except Exception as e:
            self.logger.error(f"查询设备调试信息失败异常: {e}")
            return None
# 主函数
def main():
    # 配置日志
    def setup_logging():
        """配置日志记录器"""
        # 创建日志目录（如果不存在）
        log_dir = "logs"
        if not os.path.exists(log_dir):
            os.makedirs(log_dir)

        # 生成日志文件名（包含时间戳）
        log_file = os.path.join(log_dir, f"ota_client_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log")

        # 配置根日志记录器
        root_logger = logging.getLogger()
        root_logger.setLevel(logging.INFO)

        # 清除现有的处理器
        for handler in root_logger.handlers[:]:
            root_logger.removeHandler(handler)

        # 创建控制台处理器
        console_handler = logging.StreamHandler()
        console_handler.setLevel(logging.INFO)
        console_formatter = logging.Formatter("%(asctime)s - %(name)s - %(levelname)s - %(message)s")
        console_handler.setFormatter(console_formatter)
        root_logger.addHandler(console_handler)

        # 创建文件处理器
        file_handler = logging.FileHandler(log_file, encoding="utf-8", mode="w")
        file_handler.setLevel(logging.INFO)
        file_formatter = logging.Formatter("%(asctime)s - %(name)s - %(levelname)s - %(message)s")
        file_handler.setFormatter(file_formatter)
        root_logger.addHandler(file_handler)

        # 创建并返回OTA客户端专用的日志记录器
        logger = logging.getLogger("OTA_Client")
        logger.info(f"日志文件已创建: {log_file}")
        return logger

    # 初始化日志记录器
    logger = setup_logging()

    product_key = "hs7eudkgQqD"
    device_name = "9999"
    devid = int(device_name)

    # 创建配置
    config = AmqpConfig()
    topic_filters = ["^/[^/]+/[^/]+/user/update$", "^/[^/]+/[^/]+/user/ota_ack$"]

    # 创建IoT客户端
    client = IoTClient(config, topic_filters, logger)

    # 启动客户端
    client.start()

    # 构建topic
    topic_full_name = f"/{product_key}/{device_name}/user/ota"

    # 创建寄存器管理器
    reg_manager = RegisterManager(client, topic_full_name, logger)

    # 读取T1-T13寄存器
    logger.info("读取T1-T13寄存器...")
    msg = reg_manager.read_registers(devid, RegAddr.REG_T1, 13)
    if msg:
        reg_manager.print_register_values(msg, RegAddr.REG_T1)
    else:
        logger.error("读取T1-T13寄存器失败")

    # 读取P4-P16寄存器
    logger.info("读取P4-P16寄存器...")
    msg = reg_manager.read_registers(devid, RegAddr.REG_P4, 13)
    if msg:
        reg_manager.print_register_values(msg, RegAddr.REG_P4)
    else:
        logger.error("读取P4-P16寄存器失败")

    # 写入寄存器示例（已注释）
    # logger.info("写入TEMP1寄存器...")
    # success = reg_manager.write_register(devid, RegAddr.REG_TEMP1, [85])
    # if success:
    #     logger.info("写入TEMP1寄存器成功")
    # else:
    #     logger.error("写入TEMP1寄存器失败")


if __name__ == "__main__":
    main()
