请用中文与我交流。

项目背景：
这是一个使用Python+Flask开发的充电桩管理系统的后端项目。当前我正在查看设备管理页面相关的代码，包括：
- 前端模板：devices.html（设备管理页面）
- 后端路由：routes/device.py、以及一些device相关的py文件。
- 相关的设备处理模块

当前存在的问题：
1. **页面刷新过度问题**：许多操作完成后不必要地进行页面跳转和刷新，用户体验差。具体例子：
   - 点击OTA升级功能时，OTA任务创建并提交成功后，页面会自动刷新，这是不必要的
   - 需要改为Ajax异步操作，操作完成后仅更新相关UI元素，而不是整页刷新

2. **性能问题**：设备数量大幅增加导致页面加载缓慢
   - 当前实现：一次性加载并显示所有设备，导致页面打开极其缓慢
   - 影响功能：设备全选、批量操作、设备筛选等功能都依赖于当前的全量加载方式
   - 修改复杂度：任何改动都需要考虑对现有功能的影响


解决方案要求：
需求：希望你能够完善的解决上面遇到的两个问题，在修改时注意相关的功能的实现也需要对应修改。
2. 实现设备列表的分页加载或懒加载机制，解决性能问题
3. 在优化过程中，确保以下功能正常工作：
   - 设备全选功能
   - 批量操作功能  
   - 设备筛选功能
   - OTA升级功能
4. 每个修改步骤都要考虑对相关功能的影响，确保功能完整性

请先分析当前代码结构，然后提供详细的解决方案和实施步骤。

反馈：好的，上述任务完成的非常彻底，非常棒。
下一步的任务：接下面将前面完成的任务就行依次git提交，本地提交就行，无需推送到远程。
再下一步的计划：
背景：现在项目新增了新的EMQX服务器，也就是现在的一部分设备使用阿里云，一部分设备使用EMQX自检服务器，这就需要远程更新设备的设备信息（devid、product_key），目前并未在页面上添加对应功能的按钮，而是通过外部与项目独立iot_client_server_info_update.py中的config_alicloud_to_emqx、config_emqx_to_alicloud、config_emqx_product_key三个方法实现，这导致了当前更新设备的设备信息很麻烦。
需求：1.对于上述问题，我希望在设备管理页面的每个设备上面增加一个按钮，通过模态框来配置设备信息。
2.除此之外增加一个批量配置设备信息的模态框，来实现批量配置设备信息的功能。
问题与细节告知：
1.目前来说阿里云的设备的product_key都是hs7eigK8Xvl，新的EMQX服务器的设备的product_key都是wx开头的，例如wxd48e69e833621cfd，模态框配置设备信息的时候要尽可能的减少用户的操作，例如已知设备的product_key就能知道目标能够配置的服务器类型有哪些，当然当前就只有阿里云和EMQX。
2.用户大部分情况下只需要修改product_key，而devid大部分情况下是不需要更改的。
3.能够配置的产品暂时通过一个json文件来配置，用户选择新的产品id的时候通过下拉框来选择，这样安全且方便。
4.批量修改的时候就没有办法得到一个统一的初始信息了，devid只能保持不变，而原始product_key和新的product_key、原始服务器类型和新服务器类型则需要用户自己选择，只有符合变化条件的设备会执行修改，不符合的则提示不符合未执行修改即可。

反馈：上述设备管理页面优化任务完成得非常彻底和出色。

**当前任务：Git提交**
请将前面完成的设备管理页面优化相关的所有修改进行Git提交，仅需本地提交即可，无需推送到远程仓库。

**下一步主要任务：设备服务器信息远程配置功能**

**背景说明：**
项目现在支持两种IoT服务器：
- 阿里云IoT平台：设备的product_key统一为 `hs7eigK8Xvl`
- EMQX自建服务器：设备的product_key以 `wx` 开头，例如 `wxd48e69e833621cfd`

当前存在的问题：
- 需要在两种服务器间迁移设备时，必须远程更新设备的配置信息（device_id和product_key）
- 目前只能通过独立的脚本文件 `iot_client_server_info_update.py` 中的三个方法来实现：
  - `config_alicloud_to_emqx()` - 阿里云迁移到EMQX
  - `config_emqx_to_alicloud()` - EMQX迁移到阿里云  
  - `config_emqx_product_key()` - EMQX内部product_key变更
- 操作流程复杂，用户体验差

**具体需求：**

1. **单设备配置功能**
   - 在设备管理页面的每个设备行添加"配置服务器"按钮
   - 点击按钮弹出模态框，用于配置该设备的服务器信息
   - 模态框需要调用现有的配置方法完成设备信息更新

2. **批量配置功能**
   - 在设备管理页面顶部添加"批量配置服务器"按钮，和其他的批量功能按钮放在一起，风格保持一致。
   - 支持选中多个设备进行批量服务器信息配置
   - 批量操作需要筛选符合条件的设备，不符合条件的设备给出明确提示

**设计要求和约束：**

1. **智能化配置**
   - 根据设备当前的product_key自动识别当前服务器类型
   - 阿里云设备：product_key = `hs7eigK8Xvl`
   - EMQX设备：product_key以 `wx` 开头
   - 根据当前服务器类型智能显示可配置的目标服务器选项

2. **用户操作简化**
   - device_id在大多数情况下保持不变，仅在特殊需求时允许修改
   - 重点关注product_key的修改，这是最常见的配置场景
   - 提供清晰的当前配置信息显示

3. **产品配置管理**
   - 创建JSON配置文件定义可用的产品列表和对应的product_key
   - 用户通过下拉框选择目标产品，避免手动输入错误
   - 确保配置的安全性和便捷性

4. **批量操作逻辑**
   - 批量配置时无法获取统一的初始信息，需要用户手动指定：
     - 原始服务器类型和product_key
     - 目标服务器类型和product_key
   - device_id在批量操作中保持不变
   - 只对符合筛选条件的设备执行配置更新
   - 对不符合条件的设备提供明确的跳过原因说明

**技术实现要点：**
- 复用现有的 `iot_client_server_info_update.py` 中的配置方法
- 保持与当前设备管理页面Ajax架构的一致性
- 提供详细的操作结果反馈和错误处理
- 确保配置操作的原子性和可回滚性