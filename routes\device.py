from flask import render_template, redirect, url_for, flash, request, current_app
from flask_login import login_required
from models.device import Device
from models.ota_task import OtaTask
from models.database import db
from models.firmware import Firmware
from flask import Blueprint
from flask import jsonify, send_file
import pandas as pd
from io import BytesIO
import openpyxl
from datetime import datetime
from sqlalchemy import or_, and_
from services.server_config_service import server_config_service
from services.device_server_config import device_server_config_manager

device_bp = Blueprint('device', __name__)


@device_bp.route('/devices')
@login_required
def devices():
    """设备管理页面"""
    # 只获取固件列表，设备列表通过Ajax加载
    firmwares = Firmware.query.order_by(Firmware.upload_time.desc()).all()

    # 获取设备统计信息
    total_devices = Device.query.count()
    device_status_cache = current_app.config.get('DEVICE_STATUS_CACHE', {})
    device_status_lock = current_app.config.get('DEVICE_STATUS_LOCK')

    with device_status_lock:
        # 统计在线和离线设备数量
        online_count = sum(1 for status in device_status_cache.values() if status.get('is_online', False))
        offline_count = total_devices - online_count

    # 传递空的设备列表和统计信息
    return render_template('devices.html',
                         devices=[],
                         firmwares=firmwares,
                         device_status_cache={},
                         stats={
                             'total': total_devices,
                             'online': online_count,
                             'offline': offline_count
                         })

@device_bp.route('/api/devices')
@login_required
def get_devices_api():
    """获取设备列表API（支持分页、搜索、筛选）"""
    try:
        # 获取分页参数
        page = request.args.get('page', 1, type=int)
        per_page = request.args.get('per_page', 20, type=int)

        # 获取搜索和筛选参数
        search = request.args.get('search', '').strip()
        status_filter = request.args.get('status', 'all')
        product_key_filter = request.args.get('product_key', '').strip()
        firmware_filter = request.args.get('firmware', '').strip()
        ota_status_filter = request.args.get('ota_status', 'all')

        # 构建查询
        query = Device.query

        # 搜索条件
        if search:
            query = query.filter(
                or_(
                    Device.device_id.contains(search),
                    Device.device_remark.contains(search)
                )
            )

        # 产品密钥筛选
        if product_key_filter:
            query = query.filter(Device.product_key.contains(product_key_filter))

        # 固件版本筛选
        if firmware_filter:
            query = query.filter(Device.firmware_version.contains(firmware_filter))

        # OTA状态筛选
        if ota_status_filter != 'all':
            if ota_status_filter == 'success':
                query = query.filter(Device.last_ota_status == '成功')
            elif ota_status_filter == 'failed':
                query = query.filter(Device.last_ota_status == '失败')
            elif ota_status_filter == 'none':
                query = query.filter(
                    or_(
                        Device.last_ota_status.is_(None),
                        Device.last_ota_status == '未升级',
                        Device.last_ota_status == '未知'
                    )
                )

        # 执行分页查询
        pagination = query.paginate(
            page=page,
            per_page=per_page,
            error_out=False
        )

        devices = pagination.items

        # 获取设备状态缓存
        device_status_cache = current_app.config.get('DEVICE_STATUS_CACHE', {})
        device_status_lock = current_app.config.get('DEVICE_STATUS_LOCK')

        with device_status_lock:
            current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            # 确保所有设备都有状态记录
            for device in devices:
                if device.id not in device_status_cache:
                    device_status_cache[device.id] = {
                        'is_online': False,
                        'last_check': current_time,
                        'last_online_time': '未知'
                    }

        # 构建响应数据
        devices_data = []
        for device in devices:
            status_info = device_status_cache.get(device.id, {
                'is_online': False,
                'last_check': current_time,
                'last_online_time': '未知'
            })

            devices_data.append({
                'id': device.id,
                'device_id': device.device_id,
                'device_remark': device.device_remark or '',
                'product_key': device.product_key,
                'firmware_version': device.firmware_version or '未知',
                'last_ota_status': device.last_ota_status or '未升级',
                'last_ota_time': device.last_ota_time.strftime('%Y-%m-%d %H:%M:%S') if device.last_ota_time else '未升级',
                'is_online': status_info['is_online'],
                'last_online_time': status_info['last_online_time'],
                'debug_script_enabled': device.debug_script and device.debug_script.enabled if hasattr(device, 'debug_script') and device.debug_script else False
            })

        # 状态筛选（需要在获取状态信息后进行）
        if status_filter != 'all':
            if status_filter == 'online':
                devices_data = [d for d in devices_data if d['is_online']]
            elif status_filter == 'offline':
                devices_data = [d for d in devices_data if not d['is_online']]

        return jsonify({
            'success': True,
            'devices': devices_data,
            'pagination': {
                'page': pagination.page,
                'pages': pagination.pages,
                'per_page': pagination.per_page,
                'total': pagination.total,
                'has_prev': pagination.has_prev,
                'has_next': pagination.has_next,
                'prev_num': pagination.prev_num,
                'next_num': pagination.next_num
            }
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@device_bp.route('/api/devices/stats')
@login_required
def get_devices_stats():
    """获取设备统计信息"""
    try:
        # 获取设备总数
        total_devices = Device.query.count()

        # 获取设备状态缓存
        device_status_cache = current_app.config.get('DEVICE_STATUS_CACHE', {})
        device_status_lock = current_app.config.get('DEVICE_STATUS_LOCK')

        with device_status_lock:
            # 统计在线和离线设备数量
            online_count = sum(1 for status in device_status_cache.values() if status.get('is_online', False))
            offline_count = total_devices - online_count

        return jsonify({
            'success': True,
            'stats': {
                'total': total_devices,
                'online': online_count,
                'offline': offline_count
            }
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@device_bp.route('/api/server-config/products')
@login_required
def get_server_products():
    """获取服务器产品配置信息"""
    try:
        return jsonify({
            'success': True,
            'server_types': server_config_service.get_server_types(),
            'products': server_config_service.get_all_products()
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@device_bp.route('/api/server-config/available-targets/<product_key>')
@login_required
def get_available_targets(product_key):
    """获取指定产品可迁移的目标产品列表"""
    try:
        targets = server_config_service.get_available_targets(product_key)
        current_server_type = server_config_service.detect_server_type(product_key)

        return jsonify({
            'success': True,
            'current_server_type': current_server_type,
            'available_targets': targets
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@device_bp.route('/api/device/<int:device_id>/server-config', methods=['POST'])
@login_required
def update_device_server_config(device_id):
    """更新单个设备的服务器配置"""
    try:
        device = Device.query.get_or_404(device_id)
        data = request.get_json()

        source_product_key = device.product_key
        target_product_key = data.get('target_product_key', '').strip()
        new_device_id = data.get('new_device_id', '').strip() or None

        if not target_product_key:
            return jsonify({
                'success': False,
                'message': '目标产品密钥不能为空'
            }), 400

        # 执行配置更新
        success, message = device_server_config_manager.update_device_server_config(
            device.device_id, source_product_key, target_product_key, new_device_id
        )

        if success:
            # 更新数据库中的设备信息
            if new_device_id:
                device.device_id = new_device_id
            device.product_key = target_product_key
            db.session.commit()

            return jsonify({
                'success': True,
                'message': message,
                'device': {
                    'id': device.id,
                    'device_id': device.device_id,
                    'product_key': device.product_key
                }
            })
        else:
            return jsonify({
                'success': False,
                'message': message
            }), 400

    except Exception as e:
        db.session.rollback()
        return jsonify({
            'success': False,
            'message': f'配置更新失败：{str(e)}'
        }), 500

@device_bp.route('/api/devices/batch-server-config', methods=['POST'])
@login_required
def batch_update_server_config():
    """批量更新设备服务器配置"""
    try:
        data = request.get_json()
        device_ids = data.get('device_ids', [])
        source_product_key = data.get('source_product_key', '').strip()
        target_product_key = data.get('target_product_key', '').strip()

        if not device_ids:
            return jsonify({
                'success': False,
                'message': '请选择要配置的设备'
            }), 400

        if not source_product_key or not target_product_key:
            return jsonify({
                'success': False,
                'message': '源产品密钥和目标产品密钥不能为空'
            }), 400

        # 获取符合条件的设备
        devices = Device.query.filter(
            Device.id.in_(device_ids),
            Device.product_key == source_product_key
        ).all()

        if not devices:
            return jsonify({
                'success': False,
                'message': f'没有找到产品密钥为 {source_product_key} 的设备'
            }), 400

        results = []
        success_count = 0

        for device in devices:
            try:
                # 执行配置更新
                success, message = device_server_config_manager.update_device_server_config(
                    device.device_id, source_product_key, target_product_key
                )

                if success:
                    # 更新数据库中的设备信息
                    device.product_key = target_product_key
                    success_count += 1

                results.append({
                    'device_id': device.device_id,
                    'device_name': device.device_remark or device.device_id,
                    'success': success,
                    'message': message
                })

            except Exception as e:
                results.append({
                    'device_id': device.device_id,
                    'device_name': device.device_remark or device.device_id,
                    'success': False,
                    'message': f'配置失败: {str(e)}'
                })

        # 提交数据库更改
        if success_count > 0:
            db.session.commit()

        return jsonify({
            'success': True,
            'message': f'批量配置完成，成功 {success_count}/{len(devices)} 个设备',
            'results': results,
            'summary': {
                'total': len(devices),
                'success': success_count,
                'failed': len(devices) - success_count
            }
        })

    except Exception as e:
        db.session.rollback()
        return jsonify({
            'success': False,
            'message': f'批量配置失败：{str(e)}'
        }), 500

@device_bp.route('/device/add', methods=['GET', 'POST'])
@login_required
def add_device():
    """添加设备"""
    if request.method == 'POST':
        device_id = request.form.get('device_id')
        device_remark = request.form.get('device_remark')
        product_key = request.form.get('product_key')
        
        # 检查设备ID是否已存在
        if Device.query.filter_by(device_id=device_id).first():
            flash('设备ID已存在', 'danger')
            return redirect(url_for('device.add_device'))
        
        # 创建新设备
        device = Device(
            device_id=device_id,
            device_remark=device_remark,
            product_key=product_key
        )
        
        db.session.add(device)
        db.session.commit()
        
        flash('设备添加成功', 'success')
        return redirect(url_for('device.devices'))

    return render_template('device_form.html')

@device_bp.route('/api/device/add', methods=['POST'])
@login_required
def add_device_api():
    """Ajax添加设备API"""
    try:
        data = request.get_json()
        device_id = data.get('device_id', '').strip()
        device_remark = data.get('device_remark', '').strip()
        product_key = data.get('product_key', '').strip()

        if not device_id or not product_key:
            return jsonify({
                'success': False,
                'message': '设备ID和产品密钥不能为空'
            }), 400

        # 检查设备ID是否已存在
        if Device.query.filter_by(device_id=device_id).first():
            return jsonify({
                'success': False,
                'message': '设备ID已存在'
            }), 400

        # 创建新设备
        device = Device(
            device_id=device_id,
            device_remark=device_remark,
            product_key=product_key
        )

        db.session.add(device)
        db.session.commit()

        return jsonify({
            'success': True,
            'message': '设备添加成功',
            'device': {
                'id': device.id,
                'device_id': device.device_id,
                'device_remark': device.device_remark,
                'product_key': device.product_key
            }
        })

    except Exception as e:
        db.session.rollback()
        return jsonify({
            'success': False,
            'message': f'添加设备失败：{str(e)}'
        }), 500

@device_bp.route('/device/edit/<int:id>', methods=['GET', 'POST'])
@login_required
def edit_device(id):
    """编辑设备"""
    device = Device.query.get_or_404(id)
    
    if request.method == 'POST':
        device_id = request.form.get('device_id')
        device_remark = request.form.get('device_remark')
        product_key = request.form.get('product_key')
        
        # 检查设备ID是否已被其他设备使用
        existing_device = Device.query.filter_by(device_id=device_id).first()
        if existing_device and existing_device.id != id:
            flash('设备ID已存在', 'danger')
            return redirect(url_for('device.edit_device', id=id))
        
        # 更新设备信息
        device.device_id = device_id
        device.device_remark = device_remark
        device.product_key = product_key
        
        db.session.commit()
        
        flash('设备更新成功', 'success')
        return redirect(url_for('device.devices'))
    
    return render_template('device_form.html', device=device)

@device_bp.route('/api/device/edit/<int:id>', methods=['PUT'])
@login_required
def edit_device_api(id):
    """Ajax编辑设备API"""
    try:
        device = Device.query.get_or_404(id)
        data = request.get_json()

        device_id = data.get('device_id', '').strip()
        device_remark = data.get('device_remark', '').strip()
        product_key = data.get('product_key', '').strip()

        if not device_id or not product_key:
            return jsonify({
                'success': False,
                'message': '设备ID和产品密钥不能为空'
            }), 400

        # 检查设备ID是否已被其他设备使用
        existing_device = Device.query.filter_by(device_id=device_id).first()
        if existing_device and existing_device.id != id:
            return jsonify({
                'success': False,
                'message': '设备ID已存在'
            }), 400

        # 更新设备信息
        device.device_id = device_id
        device.device_remark = device_remark
        device.product_key = product_key

        db.session.commit()

        return jsonify({
            'success': True,
            'message': '设备更新成功',
            'device': {
                'id': device.id,
                'device_id': device.device_id,
                'device_remark': device.device_remark,
                'product_key': device.product_key
            }
        })

    except Exception as e:
        db.session.rollback()
        return jsonify({
            'success': False,
            'message': f'更新设备失败：{str(e)}'
        }), 500

@device_bp.route('/device/delete/<int:id>')
@login_required
def delete_device(id):
    """删除设备"""
    device = Device.query.get_or_404(id)
    
    # 删除相关的OTA任务
    OtaTask.query.filter_by(device_id=id).delete()
    
    db.session.delete(device)
    db.session.commit()
    
    flash('设备删除成功', 'success')
    return redirect(url_for('device.devices'))

@device_bp.route('/api/device/delete/<int:id>', methods=['DELETE'])
@login_required
def delete_device_api(id):
    """Ajax删除设备API"""
    try:
        device = Device.query.get_or_404(id)

        # 删除相关的OTA任务
        OtaTask.query.filter_by(device_id=id).delete()

        db.session.delete(device)
        db.session.commit()

        return jsonify({
            'success': True,
            'message': '设备删除成功'
        })

    except Exception as e:
        db.session.rollback()
        return jsonify({
            'success': False,
            'message': f'删除设备失败：{str(e)}'
        }), 500


@device_bp.route('/batch_import_devices', methods=['POST'])
@login_required
def batch_import_devices():
    """批量导入设备"""
    if 'import_file' not in request.files:
        return jsonify({'error': '没有上传文件'}), 400
    
    file = request.files['import_file']
    if file.filename == '':
        return jsonify({'error': '没有选择文件'}), 400
    
    if not file.filename.endswith(('.xlsx', '.xls')):
        return jsonify({'error': '请上传Excel文件'}), 400
    
    try:
        # 读取Excel文件
        df = pd.read_excel(file)
        
        # 验证必要的列是否存在
        required_columns = ['设备ID', '产品密钥']
        if not all(col in df.columns for col in required_columns):
            return jsonify({'error': 'Excel文件格式不正确，请使用正确的模板'}), 400
        
        results = []
        success_count = 0
        existing_count = 0
        failed_count = 0
        
        # 处理每一行数据
        for index, row in df.iterrows():
            device_id = str(row['设备ID']).strip()
            product_key = str(row['产品密钥']).strip()
            device_remark = str(row.get('设备备注', '')).strip() if '设备备注' in df.columns else ''
            
            # 验证数据
            if not device_id or not product_key:
                results.append({
                    'device_id': device_id,
                    'status': 'failed',
                    'message': '设备ID和产品密钥不能为空'
                })
                failed_count += 1
                continue
            
            # 检查设备是否已存在
            existing_device = Device.query.filter_by(device_id=device_id).first()
            if existing_device:
                results.append({
                    'device_id': device_id,
                    'status': 'existing',
                    'message': '设备已存在'
                })
                existing_count += 1
                continue
            
            try:
                # 创建新设备
                new_device = Device(
                    device_id=device_id,
                    product_key=product_key,
                    device_remark=device_remark
                )
                db.session.add(new_device)
                db.session.commit()
                
                results.append({
                    'device_id': device_id,
                    'status': 'success',
                    'message': '导入成功'
                })
                success_count += 1
                
            except Exception as e:
                db.session.rollback()
                results.append({
                    'device_id': device_id,
                    'status': 'failed',
                    'message': f'导入失败：{str(e)}'
                })
                failed_count += 1
        
        return jsonify({
            'success_count': success_count,
            'existing_count': existing_count,
            'failed_count': failed_count,
            'results': results
        })
        
    except Exception as e:
        return jsonify({'error': f'处理文件时发生错误：{str(e)}'}), 500

@device_bp.route('/download_import_template')
@login_required
def download_import_template():
    """下载导入模板"""
    try:
        # 创建示例数据
        data = {
            '设备ID': ['DEVICE001', 'DEVICE002'],
            '产品密钥': ['PRODUCT_KEY_1', 'PRODUCT_KEY_2'],
            '设备备注': ['示例设备1', '示例设备2']
        }
        df = pd.DataFrame(data)
        
        # 创建Excel文件
        output = BytesIO()
        with pd.ExcelWriter(output, engine='openpyxl') as writer:
            df.to_excel(writer, index=False, sheet_name='设备导入模板')
            
            # 获取工作表
            worksheet = writer.sheets['设备导入模板']
            
            # 设置列宽
            worksheet.column_dimensions['A'].width = 20
            worksheet.column_dimensions['B'].width = 30
            worksheet.column_dimensions['C'].width = 30
            
            # 添加说明
            worksheet['A1'].comment = openpyxl.comments.Comment(
                '设备的唯一标识符，必填',
                '系统'
            )
            worksheet['B1'].comment = openpyxl.comments.Comment(
                '设备的产品密钥，必填',
                '系统'
            )
            worksheet['C1'].comment = openpyxl.comments.Comment(
                '设备的备注信息，选填',
                '系统'
            )
        
        output.seek(0)
        return send_file(
            output,
            mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            as_attachment=True,
            download_name='设备导入模板.xlsx'
        )
        
    except Exception as e:
        return jsonify({'error': f'生成模板时发生错误：{str(e)}'}), 500
