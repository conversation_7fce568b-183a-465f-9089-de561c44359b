from flask import render_template, redirect, url_for, flash, request, current_app
from flask_login import login_required
from models.device import Device
from models.ota_task import OtaTask
from models.database import db
from models.firmware import Firmware
from flask import Blueprint
from flask import jsonify, send_file
import pandas as pd
from io import BytesIO
import openpyxl
from datetime import datetime

device_bp = Blueprint('device', __name__)


@device_bp.route('/devices')
@login_required
def devices():
    """设备管理页面"""
    devices = Device.query.all()
    firmwares = Firmware.query.order_by(Firmware.upload_time.desc()).all()

    # 从应用上下文获取设备状态缓存
    device_status_cache = current_app.config.get('DEVICE_STATUS_CACHE', {})
    device_status_lock = current_app.config.get('DEVICE_STATUS_LOCK')

    with device_status_lock:
        current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        # 确保所有设备都有状态记录
        for device in devices:
            if device.id not in device_status_cache:
                device_status_cache[device.id] = {
                    'is_online': False,
                    'last_check': current_time,
                    'last_online_time': '未知'
                }

    return render_template('devices.html', devices=devices, firmwares=firmwares, device_status_cache=device_status_cache)

@device_bp.route('/device/add', methods=['GET', 'POST'])
@login_required
def add_device():
    """添加设备"""
    if request.method == 'POST':
        device_id = request.form.get('device_id')
        device_remark = request.form.get('device_remark')
        product_key = request.form.get('product_key')
        
        # 检查设备ID是否已存在
        if Device.query.filter_by(device_id=device_id).first():
            flash('设备ID已存在', 'danger')
            return redirect(url_for('device.add_device'))
        
        # 创建新设备
        device = Device(
            device_id=device_id,
            device_remark=device_remark,
            product_key=product_key
        )
        
        db.session.add(device)
        db.session.commit()
        
        flash('设备添加成功', 'success')
        return redirect(url_for('device.devices'))
    
    return render_template('device_form.html')

@device_bp.route('/device/edit/<int:id>', methods=['GET', 'POST'])
@login_required
def edit_device(id):
    """编辑设备"""
    device = Device.query.get_or_404(id)
    
    if request.method == 'POST':
        device_id = request.form.get('device_id')
        device_remark = request.form.get('device_remark')
        product_key = request.form.get('product_key')
        
        # 检查设备ID是否已被其他设备使用
        existing_device = Device.query.filter_by(device_id=device_id).first()
        if existing_device and existing_device.id != id:
            flash('设备ID已存在', 'danger')
            return redirect(url_for('device.edit_device', id=id))
        
        # 更新设备信息
        device.device_id = device_id
        device.device_remark = device_remark
        device.product_key = product_key
        
        db.session.commit()
        
        flash('设备更新成功', 'success')
        return redirect(url_for('device.devices'))
    
    return render_template('device_form.html', device=device)

@device_bp.route('/device/delete/<int:id>')
@login_required
def delete_device(id):
    """删除设备"""
    device = Device.query.get_or_404(id)
    
    # 删除相关的OTA任务
    OtaTask.query.filter_by(device_id=id).delete()
    
    db.session.delete(device)
    db.session.commit()
    
    flash('设备删除成功', 'success')
    return redirect(url_for('device.devices'))


@device_bp.route('/batch_import_devices', methods=['POST'])
@login_required
def batch_import_devices():
    """批量导入设备"""
    if 'import_file' not in request.files:
        return jsonify({'error': '没有上传文件'}), 400
    
    file = request.files['import_file']
    if file.filename == '':
        return jsonify({'error': '没有选择文件'}), 400
    
    if not file.filename.endswith(('.xlsx', '.xls')):
        return jsonify({'error': '请上传Excel文件'}), 400
    
    try:
        # 读取Excel文件
        df = pd.read_excel(file)
        
        # 验证必要的列是否存在
        required_columns = ['设备ID', '产品密钥']
        if not all(col in df.columns for col in required_columns):
            return jsonify({'error': 'Excel文件格式不正确，请使用正确的模板'}), 400
        
        results = []
        success_count = 0
        existing_count = 0
        failed_count = 0
        
        # 处理每一行数据
        for index, row in df.iterrows():
            device_id = str(row['设备ID']).strip()
            product_key = str(row['产品密钥']).strip()
            device_remark = str(row.get('设备备注', '')).strip() if '设备备注' in df.columns else ''
            
            # 验证数据
            if not device_id or not product_key:
                results.append({
                    'device_id': device_id,
                    'status': 'failed',
                    'message': '设备ID和产品密钥不能为空'
                })
                failed_count += 1
                continue
            
            # 检查设备是否已存在
            existing_device = Device.query.filter_by(device_id=device_id).first()
            if existing_device:
                results.append({
                    'device_id': device_id,
                    'status': 'existing',
                    'message': '设备已存在'
                })
                existing_count += 1
                continue
            
            try:
                # 创建新设备
                new_device = Device(
                    device_id=device_id,
                    product_key=product_key,
                    device_remark=device_remark
                )
                db.session.add(new_device)
                db.session.commit()
                
                results.append({
                    'device_id': device_id,
                    'status': 'success',
                    'message': '导入成功'
                })
                success_count += 1
                
            except Exception as e:
                db.session.rollback()
                results.append({
                    'device_id': device_id,
                    'status': 'failed',
                    'message': f'导入失败：{str(e)}'
                })
                failed_count += 1
        
        return jsonify({
            'success_count': success_count,
            'existing_count': existing_count,
            'failed_count': failed_count,
            'results': results
        })
        
    except Exception as e:
        return jsonify({'error': f'处理文件时发生错误：{str(e)}'}), 500

@device_bp.route('/download_import_template')
@login_required
def download_import_template():
    """下载导入模板"""
    try:
        # 创建示例数据
        data = {
            '设备ID': ['DEVICE001', 'DEVICE002'],
            '产品密钥': ['PRODUCT_KEY_1', 'PRODUCT_KEY_2'],
            '设备备注': ['示例设备1', '示例设备2']
        }
        df = pd.DataFrame(data)
        
        # 创建Excel文件
        output = BytesIO()
        with pd.ExcelWriter(output, engine='openpyxl') as writer:
            df.to_excel(writer, index=False, sheet_name='设备导入模板')
            
            # 获取工作表
            worksheet = writer.sheets['设备导入模板']
            
            # 设置列宽
            worksheet.column_dimensions['A'].width = 20
            worksheet.column_dimensions['B'].width = 30
            worksheet.column_dimensions['C'].width = 30
            
            # 添加说明
            worksheet['A1'].comment = openpyxl.comments.Comment(
                '设备的唯一标识符，必填',
                '系统'
            )
            worksheet['B1'].comment = openpyxl.comments.Comment(
                '设备的产品密钥，必填',
                '系统'
            )
            worksheet['C1'].comment = openpyxl.comments.Comment(
                '设备的备注信息，选填',
                '系统'
            )
        
        output.seek(0)
        return send_file(
            output,
            mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            as_attachment=True,
            download_name='设备导入模板.xlsx'
        )
        
    except Exception as e:
        return jsonify({'error': f'生成模板时发生错误：{str(e)}'}), 500
