#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
设备服务器配置模块
集成IoT客户端配置方法，提供设备服务器信息更新功能
"""

import logging
import time
from typing import Dict, Optional, Tuple
from flask import current_app

# 导入IoT客户端相关模块
try:
    from iot_client.platform.ali_mqtt_client import AmqpConfig
    from iot_client.platform.emqx_mqtt_client import EMQXConfig
    from iot_client.platform.platform_type import PlatformType
    from iot_client.iot_client import IoTClient
    from services.iot_client_manager import IoTClientManager
    from iot_client.bin_block.bin_block import BinBlock
    from iot_client.functions.register_manager import RegisterManager
    from iot_client.bin_block.bin_block import BinBlockMQTTBrokerInfo_t
    from iot_client.bin_block.protocol_constants import MqttBrokerType

    IOT_CLIENT_AVAILABLE = True
except ImportError as e:
    IOT_CLIENT_AVAILABLE = False
    logging.warning(f"IoT客户端模块不可用: {e}")

from .server_config_service import server_config_service


class DeviceServerConfigManager:
    """设备服务器配置管理器"""

    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self._iot_client: IoTClient = None
        self._client_initialized = False

    def _initialize_iot_client(self) -> bool:
        """初始化IoT客户端"""
        if not IOT_CLIENT_AVAILABLE:
            self.logger.error("IoT客户端模块不可用，无法进行设备配置")
            return False

        if self._client_initialized:
            return True

        try:
            # 创建IoT客户端
            # 使用全局IoT客户端
            self._iot_client = IoTClientManager.get_instance()

            self._client_initialized = True
            self.logger.info("IoT客户端初始化成功")
            return True

        except Exception as e:
            self.logger.error(f"IoT客户端初始化失败: {e}")
            return False

    def _get_broker_type(self, server_type: str):
        """获取broker类型枚举"""
        if server_type == "alicloud":
            return MqttBrokerType.ALIBABA_CLOUD
        elif server_type == "emqx":
            return MqttBrokerType.EMQX
        else:
            raise ValueError(f"不支持的服务器类型: {server_type}")

    def config_alicloud_to_emqx(
        self, device_id: str, source_product_key: str, target_product_key: str, new_device_id: Optional[str] = None
    ) -> Tuple[bool, str]:
        """阿里云迁移到EMQX"""
        if not self._initialize_iot_client():
            return False, "IoT客户端初始化失败"

        try:
            if new_device_id is None:
                new_device_id = device_id

            # 构建topic
            topic_full_name = f"/{source_product_key}/{device_id}/user/ota"

            # 获取EMQX服务器配置
            emqx_config = server_config_service.get_server_config("emqx")
            default_config = emqx_config.get("default_config", {})

            # 创建broker信息
            broker_info = BinBlockMQTTBrokerInfo_t(
                new_devid=new_device_id,
                broker_type=MqttBrokerType.EMQX,
                port=default_config.get("port", 1883),
                keep_alive=0,  # 不更新
                broker_host=default_config.get("broker_host", "mqtt01.yunpusher.com"),
                username=default_config.get("username", "kafang@"),
                password=default_config.get("password", "kafang@_2025"),
                product_key=target_product_key,
                device_secret="",
            )

            # 创建寄存器管理器并执行配置
            reg_manager = RegisterManager(self._iot_client, topic_full_name, self.logger)
            result = reg_manager.cmd_mqtt_broker_info_update(device_id, broker_info, restart=True, timeout=10)

            if result.get("parsed_data", {}).get("result", None) == 0:
                return True, "配置更新成功"
            else:
                return False, f"配置更新失败: {result}"

        except Exception as e:
            self.logger.error(f"阿里云到EMQX配置失败: {e}")
            return False, f"配置失败: {str(e)}"

    def config_emqx_to_alicloud(
        self,
        device_id: str,
        source_product_key: str,
        target_product_key: str,
        device_secret: str,
        new_device_id: Optional[str] = None,
    ) -> Tuple[bool, str]:
        """EMQX迁移到阿里云"""
        if not self._initialize_iot_client():
            return False, "IoT客户端初始化失败"

        try:
            if new_device_id is None:
                new_device_id = device_id

            # 构建topic
            topic_full_name = f"/{source_product_key}/{device_id}/user/ota"

            # 创建broker信息
            broker_info = BinBlockMQTTBrokerInfo_t(
                new_devid=9999,  # 阿里云使用固定值
                broker_type=MqttBrokerType.ALIBABA_CLOUD,
                port=1883,
                keep_alive=0,  # 不更新，使用原来的
                broker_host="",  # 不更新,阿里云有product_key即可
                username="",  # 不更新，阿里云不需要
                password="",  # 不更新，阿里云不需要
                product_key=target_product_key,
                device_secret=device_secret,
            )

            # 创建寄存器管理器并执行配置
            reg_manager = RegisterManager(self._iot_client, topic_full_name, self.logger)
            result = reg_manager.cmd_mqtt_broker_info_update(device_id, broker_info, restart=True, timeout=10)

            if result.get("parsed_data", {}).get("result", None) == 0:
                return True, "配置更新成功"
            else:
                return False, f"配置更新失败: {result}"

        except Exception as e:
            self.logger.error(f"EMQX到阿里云配置失败: {e}")
            return False, f"配置失败: {str(e)}"

    def config_emqx_product_key(
        self, device_id: str, source_product_key: str, target_product_key: str
    ) -> Tuple[bool, str]:
        """EMQX内部product_key变更"""
        if not self._initialize_iot_client():
            return False, "IoT客户端初始化失败"

        try:
            # 构建topic
            topic_full_name = f"/{source_product_key}/{device_id}/user/ota"

            # 获取EMQX服务器配置
            emqx_config = server_config_service.get_server_config("emqx")
            default_config = emqx_config.get("default_config", {})

            # 创建broker信息
            broker_info = BinBlockMQTTBrokerInfo_t(
                new_devid=device_id,
                broker_type=MqttBrokerType.EMQX,
                port=default_config.get("port", 1883),
                keep_alive=0,  # 不更新
                broker_host=default_config.get("broker_host", "mqtt01.yunpusher.com"),
                username=default_config.get("username", "kafang@"),
                password=default_config.get("password", "kafang@_2025"),
                product_key=target_product_key,
                device_secret="",
            )

            # 创建寄存器管理器并执行配置
            reg_manager = RegisterManager(self._iot_client, topic_full_name, self.logger)
            result = reg_manager.cmd_mqtt_broker_info_update(device_id, broker_info, restart=True, timeout=10)

            if result.get("parsed_data", {}).get("result", None) == 0:
                return True, "配置更新成功"
            else:
                return False, f"配置更新失败: {result}"

        except Exception as e:
            self.logger.error(f"EMQX产品密钥配置失败: {e}")
            return False, f"配置失败: {str(e)}"

    def update_device_server_config(
        self, device_id: str, source_product_key: str, target_product_key: str, new_device_id: Optional[str] = None
    ) -> Tuple[bool, str]:
        """统一的设备服务器配置更新接口"""
        source_server_type = server_config_service.detect_server_type(source_product_key)
        target_server_type = server_config_service.detect_server_type(target_product_key)

        # 验证配置
        is_valid, message = server_config_service.validate_migration(source_product_key, target_product_key)
        if not is_valid:
            return False, message

        try:
            if source_server_type == "alicloud" and target_server_type == "emqx":
                return self.config_alicloud_to_emqx(device_id, source_product_key, target_product_key, new_device_id)
            elif source_server_type == "emqx" and target_server_type == "alicloud":
                # 获取阿里云产品的device_secret
                target_product = server_config_service.get_product_by_key(target_product_key)
                device_secret = target_product.get("device_secret", "")
                if not device_secret:
                    return False, "阿里云产品缺少device_secret配置"
                return self.config_emqx_to_alicloud(
                    device_id, source_product_key, target_product_key, device_secret, new_device_id
                )
            elif source_server_type == "emqx" and target_server_type == "emqx":
                return self.config_emqx_product_key(device_id, source_product_key, target_product_key)
            else:
                return False, f"不支持的配置类型: {source_server_type} -> {target_server_type}"

        except Exception as e:
            self.logger.error(f"设备服务器配置更新失败: {e}")
            return False, f"配置更新失败: {str(e)}"

    def cleanup(self):
        """清理资源"""
        if self._iot_client and self._client_initialized:
            try:
                # 这里可以添加客户端清理逻辑
                pass
            except Exception as e:
                self.logger.error(f"清理IoT客户端失败: {e}")


# 创建全局配置管理器实例
device_server_config_manager = DeviceServerConfigManager()
