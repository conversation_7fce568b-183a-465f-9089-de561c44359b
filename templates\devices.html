{% extends "base.html" %}

{% block title %}设备管理{% endblock %}

{% block content %}
<div class="container-fluid mt-4">
    <!-- 页面标题和统计卡片 -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <h2 class="mb-0"><i class="fas fa-microchip text-primary"></i> 设备管理</h2>
                <div class="btn-group">
                    <button type="button" class="btn btn-success" data-bs-toggle="modal" data-bs-target="#batchImportModal">
                        <i class="fas fa-file-import"></i> 批量导入
                    </button>
                    <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addDeviceModal">
                        <i class="fas fa-plus"></i> 添加设备
                    </button>
                    <button type="button" class="btn btn-warning batch-ota-btn">
                        <i class="fas fa-sync-alt"></i> 批量升级
                    </button>
                    <button type="button" class="btn btn-info" onclick="showBatchSetParametersModal()">
                        <i class="fas fa-cogs"></i> 批量设置参数
                    </button>
                    <button type="button" class="btn btn-warning" onclick="showBatchQueryParametersModal()">
                        <i class="fas fa-search"></i> 批量查询参数
                    </button>
                    <button type="button" class="btn btn-secondary" onclick="showBatchQueryLocationsModal()">
                        <i class="fas fa-map-marker-alt"></i> 批量查询位置
                    </button>
                    <button type="button" class="btn btn-danger" onclick="showBatchDebugScriptModal()">
                        <i class="fas fa-terminal"></i> 批量调试脚本
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 统计卡片 -->
    <div class="row mb-4" id="statsCards">
        <div class="col-md-4">
            <div class="card border-primary h-100">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <i class="fas fa-server fa-3x text-primary"></i>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <h6 class="card-title text-muted mb-1">设备总数</h6>
                            <h3 class="mb-0" id="totalDevices">{{ stats.total if stats else 0 }}</h3>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="card border-success h-100">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <i class="fas fa-check-circle fa-3x text-success"></i>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <h6 class="card-title text-muted mb-1">在线设备</h6>
                            <h3 class="mb-0" id="onlineDevices">{{ stats.online if stats else 0 }}</h3>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="card border-warning h-100">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <i class="fas fa-exclamation-triangle fa-3x text-warning"></i>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <h6 class="card-title text-muted mb-1">离线设备</h6>
                            <h3 class="mb-0" id="offlineDevices">{{ stats.offline if stats else 0 }}</h3>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 设备列表 -->
    <div class="card shadow-sm">
        <div class="card-header bg-white py-3">
            <div class="row align-items-center">
                <div class="col">
                    <h5 class="mb-0"><i class="fas fa-list text-primary"></i> 设备列表</h5>
                </div>
            </div>
            <!-- 添加筛选功能区域 -->
            <div class="row mt-3">
                <div class="col-12 d-flex justify-content-end gap-3 flex-wrap">
                    <div class="input-group" style="width: 200px;">
                        <span class="input-group-text"><i class="fas fa-filter"></i></span>
                        <select class="form-select" id="statusFilter">
                            <option value="all">所有状态</option>
                            <option value="online">在线设备</option>
                            <option value="offline">离线设备</option>
                        </select>
                    </div>
                    <div class="input-group" style="width: 200px;">
                        <span class="input-group-text"><i class="fas fa-key"></i></span>
                        <input type="text" class="form-control" id="productKeyFilter" placeholder="产品密钥...">
                    </div>
                    <div class="input-group" style="width: 200px;">
                        <span class="input-group-text"><i class="fas fa-code-branch"></i></span>
                        <input type="text" class="form-control" id="firmwareFilter" placeholder="固件版本...">
                    </div>
                    <div class="input-group" style="width: 200px;">
                        <span class="input-group-text"><i class="fas fa-sync-alt"></i></span>
                        <select class="form-select" id="otaStatusFilter">
                            <option value="all">所有升级状态</option>
                            <option value="success">升级成功</option>
                            <option value="failed">升级失败</option>
                            <option value="none">未升级</option>
                        </select>
                    </div>
                    <div class="input-group" style="width: 300px;">
                        <input type="text" class="form-control" id="searchInput" placeholder="搜索设备ID或名称...">
                        <button class="btn btn-outline-secondary" type="button">
                            <i class="fas fa-search"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>
        <div class="card-body p-0">
            <!-- 加载状态 -->
            <div id="loadingIndicator" class="text-center p-4" style="display: none;">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">加载中...</span>
                </div>
                <div class="mt-2">正在加载设备列表...</div>
            </div>

            <div class="table-responsive">
                <table class="table table-hover mb-0">
                    <thead class="table-light">
                        <tr>
                            <th style="width: 40px;">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="selectAllDevices">
                                </div>
                            </th>
                            <th style="min-width: 120px;">设备ID</th>
                            <th style="min-width: 150px;">设备备注</th>
                            <th style="width: 80px;">状态</th>
                            <th style="min-width: 120px;">产品密钥</th>
                            <th style="min-width: 100px;">固件版本</th>
                            <th style="width: 90px; white-space: nowrap;">升级状态</th>
                            <th style="min-width: 160px;">最后升级时间</th>
                            <th style="min-width: 160px;">最后在线时间</th>
                            <th style="width: 100px;">调试状态</th>
                            <th class="text-end" style="min-width: 150px;">操作</th>
                        </tr>
                    </thead>
                    <tbody id="deviceTableBody">
                        <!-- 设备列表将通过Ajax动态加载 -->
                    </tbody>
                </table>
            </div>

            <!-- 分页控件 -->
            <div class="d-flex justify-content-between align-items-center p-3 border-top">
                <div class="text-muted">
                    <span id="paginationInfo">显示第 1-20 条，共 0 条记录</span>
                </div>
                <nav aria-label="设备列表分页">
                    <ul class="pagination pagination-sm mb-0" id="paginationControls">
                        <!-- 分页按钮将通过JavaScript动态生成 -->
                    </ul>
                </nav>
            </div>
        </div>
    </div>
</div>

<!-- 添加设备模态框 -->
<div class="modal fade" id="addDeviceModal" tabindex="-1" aria-labelledby="addDeviceModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="addDeviceModalLabel">
                    <i class="fas fa-plus-circle text-primary me-2"></i>添加设备
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form id="addDeviceForm">
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="device_id" class="form-label">设备ID</label>
                        <div class="input-group">
                            <span class="input-group-text"><i class="fas fa-microchip"></i></span>
                            <input type="text" class="form-control" id="device_id" name="device_id" required>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label for="device_remark" class="form-label">设备备注</label>
                        <div class="input-group">
                            <span class="input-group-text"><i class="fas fa-tag"></i></span>
                            <input type="text" class="form-control" id="device_remark" name="device_remark">
                        </div>
                    </div>
                    <div class="mb-3">
                        <label for="product_key" class="form-label">产品密钥</label>
                        <div class="input-group">
                            <span class="input-group-text"><i class="fas fa-key"></i></span>
                            <input type="text" class="form-control" id="product_key" name="product_key" required>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                        <i class="fas fa-times me-1"></i>取消
                    </button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save me-1"></i>添加
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- 批量导入模态框 -->
<div class="modal fade" id="batchImportModal" tabindex="-1" aria-labelledby="batchImportModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="batchImportModalLabel">
                    <i class="fas fa-file-import text-success me-2"></i>批量导入设备
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form id="batchImportForm" action="{{ url_for('device.batch_import_devices') }}" method="POST" enctype="multipart/form-data">
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="import_file" class="form-label">选择Excel文件</label>
                        <div class="input-group">
                            <input type="file" class="form-control" id="import_file" name="import_file" accept=".xlsx,.xls" required>
                            <button class="btn btn-outline-secondary" type="button" id="downloadTemplate">
                                <i class="fas fa-download"></i> 下载模板
                            </button>
                        </div>
                        <div class="form-text">请使用Excel文件，包含设备ID、设备备注、产品密钥等字段</div>
                    </div>
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>导入说明：
                        <ul class="mb-0 mt-2">
                            <li>设备ID为必填项，且必须唯一</li>
                            <li>已存在的设备ID将被跳过</li>
                            <li>产品密钥为必填项</li>
                            <li>设备备注为选填项</li>
                        </ul>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="submit" class="btn btn-success">
                        <i class="fas fa-file-import me-1"></i>开始导入
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- 导入结果模态框 -->
<div class="modal fade" id="importResultModal" tabindex="-1" aria-labelledby="importResultModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="importResultModalLabel">
                    <i class="fas fa-clipboard-check text-primary me-2"></i>导入结果
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="row mb-3">
                    <div class="col-md-4">
                        <div class="card bg-success text-white">
                            <div class="card-body">
                                <h6 class="card-title">成功导入</h6>
                                <h3 class="mb-0" id="successCount">0</h3>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="card bg-warning text-white">
                            <div class="card-body">
                                <h6 class="card-title">已存在</h6>
                                <h3 class="mb-0" id="existingCount">0</h3>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="card bg-danger text-white">
                            <div class="card-body">
                                <h6 class="card-title">导入失败</h6>
                                <h3 class="mb-0" id="failedCount">0</h3>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>设备ID</th>
                                <th>状态</th>
                                <th>原因</th>
                            </tr>
                        </thead>
                        <tbody id="importResultTable">
                        </tbody>
                    </table>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                <button type="button" class="btn btn-primary" onclick="window.location.reload()">
                    <i class="fas fa-sync-alt me-1"></i>刷新列表
                </button>
            </div>
        </div>
    </div>
</div>

<!-- 批量设置参数模态框 -->
<div class="modal fade" id="batchSetParametersModal" tabindex="-1" aria-labelledby="batchSetParametersModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="batchSetParametersModalLabel">
                    <i class="fas fa-cogs text-primary me-2"></i>批量设置参数
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="batchSetParametersForm">
                    <!-- 选中的设备列表 -->
                    <div class="mb-4">
                        <h6 class="mb-3"><i class="fas fa-microchip me-2"></i>已选设备</h6>
                        <div class="list-group" id="selectedDevicesList" style="max-height: 200px; overflow-y: auto;">
                            <!-- 设备列表将通过JavaScript动态填充 -->
                        </div>
                    </div>

                    <!-- 参数设置 -->
                    <div class="mb-4">
                        <h6 class="mb-3"><i class="fas fa-sliders-h me-2"></i>参数设置</h6>
                        <div class="row g-3">
                            <div class="col-md-6">
                                <label for="batchParamName" class="form-label">参数名称</label>
                                <select class="form-select" id="batchParamName" required>
                                    <option value="">请选择参数...</option>
                                    <option value="REG_T1">REG_T1 - 长时间未插入充电器检测时间</option>
                                    <option value="REG_T2">REG_T2 - 功率大于0连续时间</option>
                                    <option value="REG_T3">REG_T3 - 浮充时间</option>
                                    <option value="REG_T4">REG_T4 - 功率超过限制判定时间</option>
                                    <option value="REG_T5">REG_T5 - 总功率超过限制触发时间</option>
                                    <option value="REG_T6">REG_T6 - 温度超过阈值判定时间</option>
                                    <option value="REG_T7">REG_T7 - 初始单个口功率过大判定时间</option>
                                    <option value="REG_T8">REG_T8 - 充电过程中继电器开路状态判断时间</option>
                                    <option value="REG_T9">REG_T9 - 首次进入充电过程中功率突降为0时的浮充时间</option>
                                    <option value="REG_T10">REG_T10 - 无线充电浮充时间</option>
                                    <option value="REG_P1">REG_P1 - 浮充功率阈值</option>
                                    <option value="REG_P2">REG_P2 - 单口充电过程中的功率限制</option>
                                    <option value="REG_P3">REG_P3 - 单口充电过程中的安全功率限制</option>
                                    <option value="REG_P4">REG_P4 - 总功率限制</option>
                                    <option value="REG_P5">REG_P5 - 单口初始安全功率限制</option>
                                    <option value="REG_P6">REG_P6 - 启动充电后检测充电负载存在阈值</option>
                                    <option value="REG_P7">REG_P7 - 无线充电浮充功率阈值</option>
                                    <option value="REG_P8">REG_P8 - 判断是否接入用电设备的阈值，小于这个阈值判定为用电设备断开与插座的连接</option>
                                    <option value="REG_T11">REG_T11 - 拔出充电器的判定时间</option>
                                    <option value="REG_CTRL1">REG_CTRL1 - 控制寄存器</option>
                                    <option value="REG_TEMP1">REG_TEMP1 - 过温保护阈值</option>
                                    <option value="REG_PERSENTAGE">REG_PERSENTAGE - 拔出插头判定百分比</option>
                                </select>
                            </div>
                            <div class="col-md-6">
                                <label for="batchParamValue" class="form-label">参数值</label>
                                <input type="number" class="form-control" id="batchParamValue" required>
                                <div class="form-text" id="paramDescription"></div>
                            </div>
                        </div>
                    </div>

                    <!-- 设置记录 -->
                    <div class="mb-4">
                        <h6 class="mb-3"><i class="fas fa-history me-2"></i>设置记录</h6>
                        <!-- 总体设置结果 -->
                        <div class="alert alert-info mb-3" id="batchSetSummary" style="display: none;">
                            <div class="d-flex align-items-center">
                                <i class="fas fa-info-circle me-2"></i>
                                <div>
                                    <strong>总体设置结果：</strong>
                                    <span id="batchSetSummaryText"></span>
                                </div>
                            </div>
                        </div>
                        <div class="table-responsive">
                            <table class="table table-sm table-hover">
                                <thead>
                                    <tr>
                                        <th>设备ID</th>
                                        <th>设备备注</th>
                                        <th>参数名称</th>
                                        <th>设置值</th>
                                        <th>状态</th>
                                        <th>错误信息</th>
                                    </tr>
                                </thead>
                                <tbody id="batchSetRecords">
                                    <!-- 设置记录将通过JavaScript动态填充 -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" onclick="batchSetParameters()">
                    <i class="fas fa-save me-1"></i>保存设置
                </button>
            </div>
        </div>
    </div>
</div>

<!-- 批量查询参数模态框 -->
<div class="modal fade" id="batchQueryParametersModal" tabindex="-1" aria-labelledby="batchQueryParametersModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="batchQueryParametersModalLabel">
                    <i class="fas fa-search text-warning me-2"></i>批量查询参数
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <!-- 选中的设备列表 -->
                <div class="mb-4">
                    <h6 class="mb-3"><i class="fas fa-microchip me-2"></i>已选设备</h6>
                    <div class="list-group" id="querySelectedDevicesList" style="max-height: 200px; overflow-y: auto;">
                        <!-- 设备列表将通过JavaScript动态填充 -->
                    </div>
                </div>

                <!-- 查询结果 -->
                <div class="mb-4">
                    <h6 class="mb-3"><i class="fas fa-list-alt me-2"></i>查询结果</h6>
                    <div class="alert alert-info mb-3" id="batchQuerySummary" style="display: none;">
                        <div class="d-flex align-items-center">
                            <i class="fas fa-info-circle me-2"></i>
                            <div>
                                <strong>查询结果：</strong>
                                <span id="batchQuerySummaryText"></span>
                            </div>
                        </div>
                    </div>
                    <div class="table-responsive">
                        <table class="table table-sm table-hover">
                            <thead>
                                <tr>
                                    <th>设备ID</th>
                                    <th>设备备注</th>
                                    <th>状态</th>
                                    <th>错误信息</th>
                                </tr>
                            </thead>
                            <tbody id="batchQueryRecords">
                                <!-- 查询记录将通过JavaScript动态填充 -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-warning" onclick="confirmBatchQueryParameters()">
                    <i class="fas fa-search me-1"></i>确认查询
                </button>
            </div>
        </div>
    </div>
</div>

<!-- 批量查询位置模态框 -->
<div class="modal fade" id="batchQueryLocationsModal" tabindex="-1" aria-labelledby="batchQueryLocationsModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="batchQueryLocationsModalLabel">
                    <i class="fas fa-map-marker-alt text-secondary me-2"></i>批量查询位置
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <!-- 选中的设备列表 -->
                <div class="mb-4">
                    <h6 class="mb-3"><i class="fas fa-microchip me-2"></i>已选设备</h6>
                    <div class="list-group" id="locationSelectedDevicesList" style="max-height: 200px; overflow-y: auto;">
                        <!-- 设备列表将通过JavaScript动态填充 -->
                    </div>
                </div>

                <!-- 查询结果 -->
                <div class="mb-4">
                    <h6 class="mb-3"><i class="fas fa-list-alt me-2"></i>查询结果</h6>
                    <div class="alert alert-info mb-3" id="batchLocationSummary" style="display: none;">
                        <div class="d-flex align-items-center">
                            <i class="fas fa-info-circle me-2"></i>
                            <div>
                                <strong>查询结果：</strong>
                                <span id="batchLocationSummaryText"></span>
                            </div>
                        </div>
                    </div>
                    <div class="table-responsive">
                        <table class="table table-sm table-hover">
                            <thead>
                                <tr>
                                    <th>设备ID</th>
                                    <th>设备备注</th>
                                    <th>位置代码</th>
                                    <th>纬度</th>
                                    <th>经度</th>
                                    <th>状态</th>
                                    <th>错误信息</th>
                                </tr>
                            </thead>
                            <tbody id="batchLocationRecords">
                                <!-- 查询记录将通过JavaScript动态填充 -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-secondary" onclick="confirmBatchQueryLocations()">
                    <i class="fas fa-map-marker-alt me-1"></i>确认查询
                </button>
            </div>
        </div>
    </div>
</div>

<!-- 批量查询参数结果模态框 -->
<div class="modal fade" id="batchQueryParametersResultModal" tabindex="-1" aria-labelledby="batchQueryParametersResultModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="batchQueryParametersResultModalLabel">
                    <i class="fas fa-search text-warning me-2"></i>批量查询参数结果
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="alert alert-info mb-3" id="batchQueryParametersResultSummary">
                    <div class="d-flex align-items-center">
                        <i class="fas fa-info-circle me-2"></i>
                        <div>
                            <strong>查询结果：</strong>
                            <span id="batchQueryParametersResultSummaryText"></span>
                        </div>
                    </div>
                </div>
                <div class="table-responsive">
                    <table class="table table-sm table-hover">
                        <thead>
                            <tr>
                                <th>设备ID</th>
                                <th>设备备注</th>
                                <th>状态</th>
                                <th>错误信息</th>
                            </tr>
                        </thead>
                        <tbody id="batchQueryParametersResultTable">
                            <!-- 查询结果将通过JavaScript动态填充 -->
                        </tbody>
                    </table>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
            </div>
        </div>
    </div>
</div>

<!-- 批量查询位置结果模态框 -->
<div class="modal fade" id="batchQueryLocationsResultModal" tabindex="-1" aria-labelledby="batchQueryLocationsResultModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="batchQueryLocationsResultModalLabel">
                    <i class="fas fa-map-marker-alt text-secondary me-2"></i>批量查询位置结果
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="alert alert-info mb-3" id="batchQueryLocationsResultSummary">
                    <div class="d-flex align-items-center">
                        <i class="fas fa-info-circle me-2"></i>
                        <div>
                            <strong>查询结果：</strong>
                            <span id="batchQueryLocationsResultSummaryText"></span>
                        </div>
                    </div>
                </div>
                <div class="table-responsive">
                    <table class="table table-sm table-hover">
                        <thead>
                            <tr>
                                <th>设备ID</th>
                                <th>设备备注</th>
                                <th>位置代码</th>
                                <th>纬度</th>
                                <th>经度</th>
                                <th>状态</th>
                                <th>错误信息</th>
                            </tr>
                        </thead>
                        <tbody id="batchQueryLocationsResultTable">
                            <!-- 查询结果将通过JavaScript动态填充 -->
                        </tbody>
                    </table>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
            </div>
        </div>
    </div>
</div>

<!-- 批量调试脚本模态框 -->
<div class="modal fade" id="batchDebugScriptModal" tabindex="-1" aria-labelledby="batchDebugScriptModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="batchDebugScriptModalLabel">
                    <i class="fas fa-terminal text-danger me-2"></i>批量调试脚本
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <!-- 选中的设备列表 -->
                <div class="mb-4">
                    <h6 class="mb-3"><i class="fas fa-microchip me-2"></i>已选设备</h6>
                    <div class="list-group" id="debugScriptSelectedDevicesList" style="max-height: 200px; overflow-y: auto;">
                        <!-- 设备列表将通过JavaScript动态填充 -->
                    </div>
                </div>

                <!-- 调试脚本设置 -->
                <div class="mb-4">
                    <h6 class="mb-3"><i class="fas fa-sliders-h me-2"></i>调试脚本设置</h6>
                    <div class="row g-3">
                        <div class="col-md-6">
                            <label for="debugScriptFrequency" class="form-label">采样频率（秒）</label>
                            <input type="number" class="form-control" id="debugScriptFrequency" min="5" value="300" required>
                            <div class="form-text">最小采样频率为5秒</div>
                        </div>
                    </div>
                </div>

                <!-- 操作结果 -->
                <div class="mb-4">
                    <h6 class="mb-3"><i class="fas fa-history me-2"></i>操作结果</h6>
                    <div class="alert alert-info mb-3" id="batchDebugScriptSummary" style="display: none;">
                        <div class="d-flex align-items-center">
                            <i class="fas fa-info-circle me-2"></i>
                            <div>
                                <strong>操作结果：</strong>
                                <span id="batchDebugScriptSummaryText"></span>
                            </div>
                        </div>
                    </div>
                    <div class="table-responsive">
                        <table class="table table-sm table-hover">
                            <thead>
                                <tr>
                                    <th>设备ID</th>
                                    <th>设备备注</th>
                                    <th>操作</th>
                                    <th>状态</th>
                                    <th>错误信息</th>
                                </tr>
                            </thead>
                            <tbody id="batchDebugScriptRecords">
                                <!-- 操作记录将通过JavaScript动态填充 -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-danger" onclick="startBatchDebugScript()">
                    <i class="fas fa-play me-1"></i>启动脚本
                </button>
                <button type="button" class="btn btn-warning" onclick="stopBatchDebugScript()">
                    <i class="fas fa-stop me-1"></i>停止脚本
                </button>
            </div>
        </div>
    </div>
</div>

{% include 'components/ota_modal.html' %}
{% include 'components/batch_ota_modal.html' %}

{% endblock %}

{% block scripts %}
<script>
// 全局变量
let currentPage = 1;
let currentFilters = {};
let selectedDevices = new Set(); // 存储选中的设备ID

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    initializeDeviceList();
    setupEventListeners();
});

// 初始化设备列表
function initializeDeviceList() {
    loadDeviceStats();
    loadDeviceList(1);
}

// 通知函数
function showNotification(message, type = 'info') {
    // 创建通知元素
    const notification = document.createElement('div');
    notification.className = `alert alert-${type === 'error' ? 'danger' : type === 'success' ? 'success' : type === 'warning' ? 'warning' : 'info'} alert-dismissible fade show position-fixed`;
    notification.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    notification.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    `;

    // 添加到页面
    document.body.appendChild(notification);

    // 3秒后自动移除
    setTimeout(() => {
        if (notification.parentNode) {
            notification.parentNode.removeChild(notification);
        }
    }, 3000);
}

// 设置事件监听器
function setupEventListeners() {
    // 搜索和筛选事件
    document.getElementById('searchInput').addEventListener('keyup', debounce(handleFilterChange, 500));
    document.getElementById('statusFilter').addEventListener('change', handleFilterChange);
    document.getElementById('productKeyFilter').addEventListener('keyup', debounce(handleFilterChange, 500));
    document.getElementById('firmwareFilter').addEventListener('keyup', debounce(handleFilterChange, 500));
    document.getElementById('otaStatusFilter').addEventListener('change', handleFilterChange);

    // 全选复选框事件
    document.getElementById('selectAllDevices').addEventListener('change', handleSelectAll);
}

// 防抖函数
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// 处理筛选条件变化
function handleFilterChange() {
    currentFilters = {
        search: document.getElementById('searchInput').value.trim(),
        status: document.getElementById('statusFilter').value,
        product_key: document.getElementById('productKeyFilter').value.trim(),
        firmware: document.getElementById('firmwareFilter').value.trim(),
        ota_status: document.getElementById('otaStatusFilter').value
    };

    currentPage = 1; // 重置到第一页
    loadDeviceList(currentPage);
}

// 加载设备统计信息
function loadDeviceStats() {
    fetch('/api/devices/stats')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                document.getElementById('totalDevices').textContent = data.stats.total;
                document.getElementById('onlineDevices').textContent = data.stats.online;
                document.getElementById('offlineDevices').textContent = data.stats.offline;
            }
        })
        .catch(error => {
            console.error('加载设备统计失败:', error);
        });
}

// 加载设备列表
function loadDeviceList(page = 1) {
    showLoading(true);

    // 构建查询参数
    const params = new URLSearchParams({
        page: page,
        per_page: 20,
        ...currentFilters
    });

    fetch(`/api/devices?${params}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                renderDeviceList(data.devices);
                renderPagination(data.pagination);
                currentPage = page;
            } else {
                showNotification('加载设备列表失败: ' + data.error, 'error');
            }
        })
        .catch(error => {
            console.error('加载设备列表失败:', error);
            showNotification('加载设备列表失败，请重试', 'error');
        })
        .finally(() => {
            showLoading(false);
        });
}

// 显示/隐藏加载状态
function showLoading(show) {
    const loadingIndicator = document.getElementById('loadingIndicator');
    const tableBody = document.getElementById('deviceTableBody');

    if (show) {
        loadingIndicator.style.display = 'block';
        tableBody.style.opacity = '0.5';
    } else {
        loadingIndicator.style.display = 'none';
        tableBody.style.opacity = '1';
    }
}

// 渲染设备列表
function renderDeviceList(devices) {
    const tbody = document.getElementById('deviceTableBody');
    tbody.innerHTML = '';

    devices.forEach(device => {
        const row = createDeviceRow(device);
        tbody.appendChild(row);
    });

    // 更新全选复选框状态
    updateSelectAllCheckbox();
}

// 创建设备行
function createDeviceRow(device) {
    const row = document.createElement('tr');

    // 状态徽章
    const statusBadge = device.is_online
        ? '<span class="badge bg-success-subtle text-success"><i class="fas fa-circle me-1"></i>在线</span>'
        : '<span class="badge bg-danger-subtle text-danger"><i class="fas fa-circle me-1"></i>离线</span>';

    // 固件版本徽章
    const firmwareBadge = device.firmware_version && device.firmware_version !== '未知'
        ? `<span class="badge bg-info-subtle text-info"><i class="fas fa-code-branch me-1"></i>${device.firmware_version}</span>`
        : '<span class="badge bg-secondary-subtle text-secondary"><i class="fas fa-question-circle me-1"></i>未知版本</span>';

    // OTA状态徽章
    let otaStatusBadge;
    if (device.last_ota_status === '成功') {
        otaStatusBadge = '<span class="badge bg-success">成功</span>';
    } else if (device.last_ota_status === '失败') {
        otaStatusBadge = '<span class="badge bg-danger">失败</span>';
    } else {
        otaStatusBadge = '<span class="badge bg-secondary">未升级</span>';
    }

    // 调试状态徽章
    const debugBadge = device.debug_script_enabled
        ? '<span class="badge bg-success"><i class="fas fa-play me-1"></i>运行中</span>'
        : '<span class="badge bg-secondary"><i class="fas fa-stop me-1"></i>已停止</span>';

    row.innerHTML = `
        <td>
            <div class="form-check">
                <input class="form-check-input device-checkbox" type="checkbox" value="${device.id}"
                       ${selectedDevices.has(device.id.toString()) ? 'checked' : ''}
                       onchange="handleDeviceSelect(this)">
            </div>
        </td>
        <td class="align-middle">
            <div class="d-flex align-items-center">
                <i class="fas fa-microchip text-primary me-2"></i>
                ${device.device_id}
            </div>
        </td>
        <td class="align-middle">${device.device_remark}</td>
        <td class="align-middle">${statusBadge}</td>
        <td class="align-middle">${device.product_key}</td>
        <td class="align-middle">${firmwareBadge}</td>
        <td class="align-middle">${otaStatusBadge}</td>
        <td class="align-middle" style="white-space: nowrap;">
            <span class="text-muted">
                <i class="far fa-calendar-alt me-1"></i>
                ${device.last_ota_time}
            </span>
        </td>
        <td class="align-middle" style="white-space: nowrap;">
            <span class="text-muted">
                <i class="far fa-clock me-1"></i>
                ${device.last_online_time}
            </span>
        </td>
        <td class="align-middle">${debugBadge}</td>
        <td class="align-middle text-end">
            <div class="btn-group">
                <button class="btn btn-sm btn-primary" onclick="startOta('${device.id}')" title="升级">
                    <i class="fas fa-sync-alt"></i>
                </button>
                <a href="/ai/device_analysis/${device.id}" class="btn btn-sm btn-info" title="AI分析">
                    <i class="fas fa-brain"></i>
                </a>
                <a href="/device_parameters/${device.id}" class="btn btn-sm btn-info" title="参数">
                    <i class="fas fa-cogs"></i>
                </a>
                <button class="btn btn-sm btn-warning" onclick="editDevice(${device.id})" title="编辑">
                    <i class="fas fa-edit"></i>
                </button>
                <button class="btn btn-sm btn-danger" onclick="deleteDevice(${device.id})" title="删除">
                    <i class="fas fa-trash"></i>
                </button>
            </div>
        </td>
    `;

    return row;
}

// 渲染分页控件
function renderPagination(pagination) {
    const paginationInfo = document.getElementById('paginationInfo');
    const paginationControls = document.getElementById('paginationControls');

    // 更新分页信息
    const start = (pagination.page - 1) * pagination.per_page + 1;
    const end = Math.min(pagination.page * pagination.per_page, pagination.total);
    paginationInfo.textContent = `显示第 ${start}-${end} 条，共 ${pagination.total} 条记录`;

    // 生成分页按钮
    paginationControls.innerHTML = '';

    // 上一页按钮
    const prevLi = document.createElement('li');
    prevLi.className = `page-item ${!pagination.has_prev ? 'disabled' : ''}`;
    prevLi.innerHTML = `<a class="page-link" href="#" onclick="loadDeviceList(${pagination.prev_num || 1})">上一页</a>`;
    paginationControls.appendChild(prevLi);

    // 页码按钮
    const startPage = Math.max(1, pagination.page - 2);
    const endPage = Math.min(pagination.pages, pagination.page + 2);

    for (let i = startPage; i <= endPage; i++) {
        const li = document.createElement('li');
        li.className = `page-item ${i === pagination.page ? 'active' : ''}`;
        li.innerHTML = `<a class="page-link" href="#" onclick="loadDeviceList(${i})">${i}</a>`;
        paginationControls.appendChild(li);
    }

    // 下一页按钮
    const nextLi = document.createElement('li');
    nextLi.className = `page-item ${!pagination.has_next ? 'disabled' : ''}`;
    nextLi.innerHTML = `<a class="page-link" href="#" onclick="loadDeviceList(${pagination.next_num || pagination.pages})">下一页</a>`;
    paginationControls.appendChild(nextLi);
}

// 处理设备选择
function handleDeviceSelect(checkbox) {
    const deviceId = checkbox.value;
    if (checkbox.checked) {
        selectedDevices.add(deviceId);
    } else {
        selectedDevices.delete(deviceId);
    }
    updateSelectAllCheckbox();
}

// 处理全选
function handleSelectAll() {
    const selectAllCheckbox = document.getElementById('selectAllDevices');
    const deviceCheckboxes = document.querySelectorAll('.device-checkbox');

    deviceCheckboxes.forEach(checkbox => {
        checkbox.checked = selectAllCheckbox.checked;
        const deviceId = checkbox.value;
        if (selectAllCheckbox.checked) {
            selectedDevices.add(deviceId);
        } else {
            selectedDevices.delete(deviceId);
        }
    });
}

// 更新全选复选框状态
function updateSelectAllCheckbox() {
    const selectAllCheckbox = document.getElementById('selectAllDevices');
    const deviceCheckboxes = document.querySelectorAll('.device-checkbox');
    const checkedCount = document.querySelectorAll('.device-checkbox:checked').length;

    if (checkedCount === 0) {
        selectAllCheckbox.indeterminate = false;
        selectAllCheckbox.checked = false;
    } else if (checkedCount === deviceCheckboxes.length) {
        selectAllCheckbox.indeterminate = false;
        selectAllCheckbox.checked = true;
    } else {
        selectAllCheckbox.indeterminate = true;
        selectAllCheckbox.checked = false;
    }
}

// 刷新设备列表（供外部调用）
function refreshDeviceList() {
    loadDeviceStats();
    loadDeviceList(currentPage);
}

// 编辑设备
function editDevice(deviceId) {
    // 这里可以实现编辑设备的模态框
    // 暂时跳转到编辑页面
    window.location.href = `/device/edit/${deviceId}`;
}

// 删除设备
function deleteDevice(deviceId) {
    if (!confirm('确定要删除此设备吗？')) {
        return;
    }

    fetch(`/api/device/delete/${deviceId}`, {
        method: 'DELETE',
        headers: {
            'Content-Type': 'application/json',
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showNotification(data.message, 'success');
            selectedDevices.delete(deviceId.toString());
            refreshDeviceList();
        } else {
            showNotification(data.message, 'error');
        }
    })
    .catch(error => {
        console.error('删除设备失败:', error);
        showNotification('删除设备失败，请重试', 'error');
    });
}

// 下载模板
document.getElementById('downloadTemplate').addEventListener('click', function() {
    window.location.href = "{{ url_for('device.download_import_template') }}";
});

// 处理添加设备表单提交
document.getElementById('addDeviceForm').addEventListener('submit', function(e) {
    e.preventDefault();

    const formData = {
        device_id: document.getElementById('device_id').value.trim(),
        device_remark: document.getElementById('device_remark').value.trim(),
        product_key: document.getElementById('product_key').value.trim()
    };

    if (!formData.device_id || !formData.product_key) {
        showNotification('设备ID和产品密钥不能为空', 'error');
        return;
    }

    fetch('/api/device/add', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // 关闭模态框
            const modal = bootstrap.Modal.getInstance(document.getElementById('addDeviceModal'));
            modal.hide();

            // 重置表单
            document.getElementById('addDeviceForm').reset();

            // 显示成功消息
            showNotification(data.message, 'success');

            // 刷新设备列表
            refreshDeviceList();
        } else {
            showNotification(data.message, 'error');
        }
    })
    .catch(error => {
        console.error('添加设备失败:', error);
        showNotification('添加设备失败，请重试', 'error');
    });
});

// 处理导入表单提交
document.getElementById('batchImportForm').addEventListener('submit', function(e) {
    e.preventDefault();
    const formData = new FormData(this);

    fetch("{{ url_for('device.batch_import_devices') }}", {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        // 更新导入结果
        document.getElementById('successCount').textContent = data.success_count;
        document.getElementById('existingCount').textContent = data.existing_count;
        document.getElementById('failedCount').textContent = data.failed_count;

        // 清空并填充结果表格
        const resultTable = document.getElementById('importResultTable');
        resultTable.innerHTML = '';

        data.results.forEach(result => {
            const row = document.createElement('tr');
            row.innerHTML = `
                <td>${result.device_id}</td>
                <td>
                    <span class="badge ${result.status === 'success' ? 'bg-success' :
                                      result.status === 'existing' ? 'bg-warning' : 'bg-danger'}">
                        ${result.status === 'success' ? '成功' :
                          result.status === 'existing' ? '已存在' : '失败'}
                    </span>
                </td>
                <td>${result.message}</td>
            `;
            resultTable.appendChild(row);
        });

        // 关闭导入模态框，显示结果模态框
        const importModal = bootstrap.Modal.getInstance(document.getElementById('batchImportModal'));
        importModal.hide();
        const resultModal = new bootstrap.Modal(document.getElementById('importResultModal'));
        resultModal.show();
    })
    .catch(error => {
        alert('导入过程中发生错误：' + error);
    });
});

// 批量设置参数相关函数
function showBatchSetParametersModal() {
    // 检查是否有选中的设备
    if (selectedDevices.size === 0) {
        showNotification('请先选择要设置参数的设备', 'warning');
        return;
    }

    // 获取选中设备的详细信息并更新列表
    const devicesList = document.getElementById('selectedDevicesList');
    devicesList.innerHTML = '';

    // 从当前页面获取选中设备的信息
    const currentPageDevices = [];
    const checkboxes = document.querySelectorAll('tbody .device-checkbox:checked');

    checkboxes.forEach(function(checkbox) {
        const deviceId = checkbox.value;
        const deviceRow = checkbox.closest('tr');
        const deviceIdCell = deviceRow.querySelector('td:nth-child(2)');
        const deviceRemarkCell = deviceRow.querySelector('td:nth-child(3)');

        currentPageDevices.push({
            id: deviceId,
            deviceId: deviceIdCell.textContent.trim(),
            remark: deviceRemarkCell.textContent.trim()
        });
    });

    // 显示所有选中的设备（包括其他页面的）
    selectedDevices.forEach(deviceId => {
        // 查找当前页面是否有这个设备的详细信息
        let deviceInfo = currentPageDevices.find(d => d.id === deviceId);

        if (!deviceInfo) {
            // 如果当前页面没有，使用设备ID作为显示信息
            deviceInfo = {
                id: deviceId,
                deviceId: `设备 ${deviceId}`,
                remark: '(其他页面)'
            };
        }

        // 添加设备到列表
        const deviceItem = document.createElement('div');
        deviceItem.className = 'list-group-item';
        deviceItem.innerHTML = `
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <strong>${deviceInfo.deviceId}</strong>
                    <div class="text-muted small">${deviceInfo.remark}</div>
                </div>
                <span class="badge bg-primary">已选择</span>
            </div>
        `;
        devicesList.appendChild(deviceItem);
    });

    // 清空设置记录
    document.getElementById('batchSetRecords').innerHTML = '';
    
    // 重置表单
    document.getElementById('batchSetParametersForm').reset();
    document.getElementById('paramDescription').textContent = '';

    // 显示模态框
    const modal = new bootstrap.Modal(document.getElementById('batchSetParametersModal'));
    modal.show();
}

// 参数选择变化时更新描述
document.getElementById('batchParamName').addEventListener('change', function() {
    const paramDescription = document.getElementById('paramDescription');
    const selectedOption = this.options[this.selectedIndex];
    if (selectedOption.value) {
        paramDescription.textContent = selectedOption.text.split(' - ')[1];
    } else {
        paramDescription.textContent = '';
    }
});

// 批量设置参数
function batchSetParameters() {
    if (selectedDevices.size === 0) {
        showNotification('请选择至少一个设备', 'warning');
        return;
    }

    const paramName = document.getElementById('batchParamName').value;
    const paramValue = document.getElementById('batchParamValue').value;
    const paramAddr = getParamAddress(paramName);

    if (!paramAddr) {
        alert('无效的参数名称');
        return;
    }

    // 清空之前的记录
    document.getElementById('batchSetRecords').innerHTML = '';
    document.getElementById('batchSetSummary').style.display = 'none';

    // 为每个设备创建记录行
    const selectedDeviceIds = Array.from(selectedDevices);
    selectedDeviceIds.forEach(deviceId => {
        const recordRow = document.createElement('tr');
        recordRow.innerHTML = `
            <td>设备 ${deviceId}</td>
            <td>-</td>
            <td>${paramName}</td>
            <td>${paramValue}</td>
            <td><span class="badge bg-secondary">处理中...</span></td>
            <td>-</td>
        `;
        document.getElementById('batchSetRecords').appendChild(recordRow);
    });

    // 发送批量设置请求
    fetch('/api/devices/batch_parameters', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            device_ids: selectedDeviceIds,
            reg_addr: paramAddr,
            reg_value: parseInt(paramValue)
        }),
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // 更新每个设备的设置结果
            data.results.forEach((result, index) => {
                const recordRow = document.getElementById('batchSetRecords').children[index];
                const statusBadge = recordRow.querySelector('.badge');
                const errorCell = recordRow.querySelector('td:last-child');

                if (result.success) {
                    statusBadge.className = 'badge bg-success';
                    statusBadge.textContent = '成功';
                    errorCell.textContent = '-';
                } else {
                    statusBadge.className = 'badge bg-danger';
                    statusBadge.textContent = '失败';
                    errorCell.textContent = result.error || '未知错误';
                }
            });

            // 显示总体设置结果
            const successCount = data.results.filter(r => r.success).length;
            const failCount = data.results.length - successCount;
            const summaryDiv = document.getElementById('batchSetSummary');
            const summaryText = document.getElementById('batchSetSummaryText');
            
            if (failCount === 0) {
                summaryDiv.className = 'alert alert-success mb-3';
                summaryText.textContent = `全部设置成功 (${successCount}/${data.results.length})`;
            } else {
                summaryDiv.className = 'alert alert-warning mb-3';
                summaryText.textContent = `部分设置成功 (${successCount}/${data.results.length})，${failCount}个设备设置失败`;
            }
            summaryDiv.style.display = 'block';
        } else {
            // 显示错误信息
            const summaryDiv = document.getElementById('batchSetSummary');
            const summaryText = document.getElementById('batchSetSummaryText');
            summaryDiv.className = 'alert alert-danger mb-3';
            summaryText.textContent = `设置失败: ${data.error || '未知错误'}`;
            summaryDiv.style.display = 'block';
        }
    })
    .catch(error => {
        console.error('批量设置参数失败:', error);
        const summaryDiv = document.getElementById('batchSetSummary');
        const summaryText = document.getElementById('batchSetSummaryText');
        summaryDiv.className = 'alert alert-danger mb-3';
        summaryText.textContent = '请求失败，请重试';
        summaryDiv.style.display = 'block';
    });
}

// 获取参数地址
function getParamAddress(paramName) {
    const paramAddresses = {
        'REG_T1': 0, 'REG_T2': 1, 'REG_T3': 2, 'REG_T4': 3, 'REG_T5': 4,
        'REG_T6': 5, 'REG_T7': 6, 'REG_T8': 7, 'REG_T9': 8, 'REG_T10': 9,
        'REG_P1': 10, 'REG_P2': 11, 'REG_P3': 12, 'REG_P4': 13, 'REG_P5': 14,
        'REG_P6': 15, 'REG_P7': 16, 'REG_P8': 17, 'REG_T11': 18, 'REG_CTRL1': 19, 'REG_TEMP1': 20,
        'REG_PERSENTAGE': 24
    };
    return paramAddresses[paramName] || null;
}

// 全选/取消全选
document.getElementById('selectAllDevices').addEventListener('change', function() {
    const checkboxes = document.querySelectorAll('tbody tr:not([style*="display: none"]) .device-checkbox');
    checkboxes.forEach(checkbox => {
        checkbox.checked = this.checked;
    });
});

// 当单个复选框改变时，更新全选框状态
document.querySelectorAll('.device-checkbox').forEach(checkbox => {
    checkbox.addEventListener('change', function() {
        const visibleCheckboxes = document.querySelectorAll('tbody tr:not([style*="display: none"]) .device-checkbox');
        const checkedVisibleCheckboxes = document.querySelectorAll('tbody tr:not([style*="display: none"]) .device-checkbox:checked');
        document.getElementById('selectAllDevices').checked = visibleCheckboxes.length === checkedVisibleCheckboxes.length;
    });
});

// 批量查询参数相关函数
function showBatchQueryParametersModal() {
    if (selectedDevices.size === 0) {
        showNotification('请先选择要查询参数的设备', 'warning');
        return;
    }

    // 更新选中设备列表
    const devicesList = document.getElementById('querySelectedDevicesList');
    devicesList.innerHTML = '';

    checkboxes.forEach(function(checkbox) {
        const deviceId = checkbox.value;
        const deviceRow = checkbox.closest('tr');
        const deviceIdCell = deviceRow.querySelector('td:nth-child(2)');
        const deviceRemarkCell = deviceRow.querySelector('td:nth-child(3)');

        // 添加设备到列表
        const deviceItem = document.createElement('div');
        deviceItem.className = 'list-group-item';
        deviceItem.innerHTML = `
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <strong>${deviceIdCell.textContent}</strong>
                    <div class="text-muted small">${deviceRemarkCell.textContent}</div>
                </div>
                <span class="badge bg-primary">已选择</span>
            </div>
        `;
        devicesList.appendChild(deviceItem);

        selectedDevices.push({
            id: deviceId,
            deviceId: deviceIdCell.textContent,
            remark: deviceRemarkCell.textContent
        });
    });

    // 清空查询记录
    document.getElementById('batchQueryRecords').innerHTML = '';
    document.getElementById('batchQuerySummary').style.display = 'none';

    // 显示模态框
    const modal = new bootstrap.Modal(document.getElementById('batchQueryParametersModal'));
    modal.show();
}

function confirmBatchQueryParameters() {
    if (!confirm('确定要查询所选设备的参数吗？')) {
        return;
    }

    const selectedDeviceIds = Array.from(selectedDevices);

    // 为每个设备创建记录行
    const resultTable = document.getElementById('batchQueryParametersResultTable');
    resultTable.innerHTML = '';
    selectedDeviceIds.forEach(deviceId => {
        const recordRow = document.createElement('tr');
        recordRow.innerHTML = `
            <td>设备 ${deviceId}</td>
            <td>-</td>
            <td><span class="badge bg-secondary">查询中...</span></td>
            <td>-</td>
        `;
        resultTable.appendChild(recordRow);
    });

    // 关闭查询模态框，显示结果模态框
    const queryModal = bootstrap.Modal.getInstance(document.getElementById('batchQueryParametersModal'));
    queryModal.hide();
    const resultModal = new bootstrap.Modal(document.getElementById('batchQueryParametersResultModal'));
    resultModal.show();

    // 发送批量查询请求
    fetch('/api/devices/batch_query_parameters', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            device_ids: selectedDeviceIds
        }),
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // 更新每个设备的查询结果
            data.results.forEach((result, index) => {
                const recordRow = resultTable.children[index];
                const statusBadge = recordRow.querySelector('.badge');
                const errorCell = recordRow.querySelector('td:last-child');

                if (result.success) {
                    statusBadge.className = 'badge bg-success';
                    statusBadge.textContent = '成功';
                    errorCell.textContent = '-';
                } else {
                    statusBadge.className = 'badge bg-danger';
                    statusBadge.textContent = '失败';
                    errorCell.textContent = result.error || '未知错误';
                }
            });

            // 显示总体查询结果
            const successCount = data.results.filter(r => r.success).length;
            const failCount = data.results.length - successCount;
            const summaryDiv = document.getElementById('batchQueryParametersResultSummary');
            const summaryText = document.getElementById('batchQueryParametersResultSummaryText');
            
            if (failCount === 0) {
                summaryDiv.className = 'alert alert-success mb-3';
                summaryText.textContent = `全部查询成功 (${successCount}/${data.results.length})`;
            } else {
                summaryDiv.className = 'alert alert-warning mb-3';
                summaryText.textContent = `部分查询成功 (${successCount}/${data.results.length})，${failCount}个设备查询失败`;
            }
        } else {
            // 显示错误信息
            const summaryDiv = document.getElementById('batchQueryParametersResultSummary');
            const summaryText = document.getElementById('batchQueryParametersResultSummaryText');
            summaryDiv.className = 'alert alert-danger mb-3';
            summaryText.textContent = `查询失败: ${data.error || '未知错误'}`;
        }
    })
    .catch(error => {
        console.error('批量查询参数失败:', error);
        const summaryDiv = document.getElementById('batchQueryParametersResultSummary');
        const summaryText = document.getElementById('batchQueryParametersResultSummaryText');
        summaryDiv.className = 'alert alert-danger mb-3';
        summaryText.textContent = '请求失败，请重试';
    });
}

// 批量查询位置相关函数
function showBatchQueryLocationsModal() {
    const selectedDevices = [];
    const checkboxes = document.querySelectorAll('tbody .device-checkbox:checked');

    if (checkboxes.length === 0) {
        alert('请先选择要查询位置的设备');
        return;
    }

    // 更新选中设备列表
    const devicesList = document.getElementById('locationSelectedDevicesList');
    devicesList.innerHTML = '';

    checkboxes.forEach(function(checkbox) {
        const deviceId = checkbox.value;
        const deviceRow = checkbox.closest('tr');
        const deviceIdCell = deviceRow.querySelector('td:nth-child(2)');
        const deviceRemarkCell = deviceRow.querySelector('td:nth-child(3)');

        // 添加设备到列表
        const deviceItem = document.createElement('div');
        deviceItem.className = 'list-group-item';
        deviceItem.innerHTML = `
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <strong>${deviceIdCell.textContent}</strong>
                    <div class="text-muted small">${deviceRemarkCell.textContent}</div>
                </div>
                <span class="badge bg-primary">已选择</span>
            </div>
        `;
        devicesList.appendChild(deviceItem);

        selectedDevices.push({
            id: deviceId,
            deviceId: deviceIdCell.textContent,
            remark: deviceRemarkCell.textContent
        });
    });

    // 清空查询记录
    document.getElementById('batchLocationRecords').innerHTML = '';
    document.getElementById('batchLocationSummary').style.display = 'none';

    // 显示模态框
    const modal = new bootstrap.Modal(document.getElementById('batchQueryLocationsModal'));
    modal.show();
}

function confirmBatchQueryLocations() {
    if (!confirm('确定要查询所选设备的位置吗？')) {
        return;
    }

    const selectedDevices = Array.from(document.querySelectorAll('.device-checkbox:checked')).map(checkbox => {
        const deviceRow = checkbox.closest('tr');
        return {
            id: checkbox.value,
            deviceId: deviceRow.querySelector('td:nth-child(2)').textContent.trim(),
            remark: deviceRow.querySelector('td:nth-child(3)').textContent.trim()
        };
    });

    // 为每个设备创建记录行
    const resultTable = document.getElementById('batchQueryLocationsResultTable');
    resultTable.innerHTML = '';
    selectedDevices.forEach(device => {
        const recordRow = document.createElement('tr');
        recordRow.innerHTML = `
            <td>${device.deviceId}</td>
            <td>${device.remark}</td>
            <td>-</td>
            <td>-</td>
            <td>-</td>
            <td><span class="badge bg-secondary">查询中...</span></td>
            <td>-</td>
        `;
        resultTable.appendChild(recordRow);
    });

    // 关闭查询模态框，显示结果模态框
    const queryModal = bootstrap.Modal.getInstance(document.getElementById('batchQueryLocationsModal'));
    queryModal.hide();
    const resultModal = new bootstrap.Modal(document.getElementById('batchQueryLocationsResultModal'));
    resultModal.show();

    // 发送批量查询请求
    fetch('/api/devices/batch_query_locations', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            device_ids: selectedDevices.map(d => d.id)
        }),
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // 更新每个设备的查询结果
            data.results.forEach((result, index) => {
                const recordRow = resultTable.children[index];
                const statusBadge = recordRow.querySelector('.badge');
                const errorCell = recordRow.querySelector('td:last-child');

                if (result.success) {
                    statusBadge.className = 'badge bg-success';
                    statusBadge.textContent = '成功';
                    errorCell.textContent = '-';
                    
                    // 更新位置信息
                    const location = result.location;
                    recordRow.cells[2].textContent = location.location_code;
                    recordRow.cells[3].textContent = location.latitude.toFixed(6);
                    recordRow.cells[4].textContent = location.longitude.toFixed(6);
                } else {
                    statusBadge.className = 'badge bg-danger';
                    statusBadge.textContent = '失败';
                    errorCell.textContent = result.error || '未知错误';
                }
            });

            // 显示总体查询结果
            const successCount = data.results.filter(r => r.success).length;
            const failCount = data.results.length - successCount;
            const summaryDiv = document.getElementById('batchQueryLocationsResultSummary');
            const summaryText = document.getElementById('batchQueryLocationsResultSummaryText');
            
            if (failCount === 0) {
                summaryDiv.className = 'alert alert-success mb-3';
                summaryText.textContent = `全部查询成功 (${successCount}/${data.results.length})`;
            } else {
                summaryDiv.className = 'alert alert-warning mb-3';
                summaryText.textContent = `部分查询成功 (${successCount}/${data.results.length})，${failCount}个设备查询失败`;
            }
        } else {
            // 显示错误信息
            const summaryDiv = document.getElementById('batchQueryLocationsResultSummary');
            const summaryText = document.getElementById('batchQueryLocationsResultSummaryText');
            summaryDiv.className = 'alert alert-danger mb-3';
            summaryText.textContent = `查询失败: ${data.error || '未知错误'}`;
        }
    })
    .catch(error => {
        console.error('批量查询位置失败:', error);
        const summaryDiv = document.getElementById('batchQueryLocationsResultSummary');
        const summaryText = document.getElementById('batchQueryLocationsResultSummaryText');
        summaryDiv.className = 'alert alert-danger mb-3';
        summaryText.textContent = '请求失败，请重试';
    });
}

// 显示批量调试脚本模态框
function showBatchDebugScriptModal() {
    const selectedDevices = [];
    const checkboxes = document.querySelectorAll('tbody .device-checkbox:checked');

    if (checkboxes.length === 0) {
        alert('请先选择要操作的设备');
        return;
    }

    // 更新选中设备列表
    const devicesList = document.getElementById('debugScriptSelectedDevicesList');
    devicesList.innerHTML = '';

    checkboxes.forEach(function(checkbox) {
        const deviceId = checkbox.value;
        const deviceRow = checkbox.closest('tr');
        const deviceIdCell = deviceRow.querySelector('td:nth-child(2)');
        const deviceRemarkCell = deviceRow.querySelector('td:nth-child(3)');

        // 添加设备到列表
        const deviceItem = document.createElement('div');
        deviceItem.className = 'list-group-item';
        deviceItem.innerHTML = `
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <strong>${deviceIdCell.textContent}</strong>
                    <div class="text-muted small">${deviceRemarkCell.textContent}</div>
                </div>
                <span class="badge bg-primary">已选择</span>
            </div>
        `;
        devicesList.appendChild(deviceItem);

        selectedDevices.push({
            id: deviceId,
            deviceId: deviceIdCell.textContent,
            remark: deviceRemarkCell.textContent
        });
    });

    // 清空操作记录
    document.getElementById('batchDebugScriptRecords').innerHTML = '';
    document.getElementById('batchDebugScriptSummary').style.display = 'none';

    // 显示模态框
    const modal = new bootstrap.Modal(document.getElementById('batchDebugScriptModal'));
    modal.show();
}

// 启动批量调试脚本
function startBatchDebugScript() {
    const selectedDevices = Array.from(document.querySelectorAll('.device-checkbox:checked')).map(checkbox => {
        const deviceRow = checkbox.closest('tr');
        return {
            id: checkbox.value,
            deviceId: deviceRow.querySelector('td:nth-child(2)').textContent.trim(),
            remark: deviceRow.querySelector('td:nth-child(3)').textContent.trim()
        };
    });

    const frequency = parseInt(document.getElementById('debugScriptFrequency').value);
    if (frequency < 5) {
        alert('采样频率不能小于5秒');
        return;
    }

    // 为每个设备创建记录行
    const resultTable = document.getElementById('batchDebugScriptRecords');
    resultTable.innerHTML = '';
    selectedDevices.forEach(device => {
        const recordRow = document.createElement('tr');
        recordRow.innerHTML = `
            <td>${device.deviceId}</td>
            <td>${device.remark}</td>
            <td>启动脚本</td>
            <td><span class="badge bg-secondary">处理中...</span></td>
            <td>-</td>
        `;
        resultTable.appendChild(recordRow);
    });

    // 发送批量启动请求
    fetch('/debug_script/api/devices/batch_start_debug_script', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            device_ids: selectedDevices.map(d => parseInt(d.id)),
            frequency: frequency
        }),
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // 更新每个设备的操作结果
            data.results.forEach((result, index) => {
                const recordRow = resultTable.children[index];
                const statusBadge = recordRow.querySelector('.badge');
                const errorCell = recordRow.querySelector('td:last-child');

                if (result.success) {
                    statusBadge.className = 'badge bg-success';
                    statusBadge.textContent = '成功';
                    errorCell.textContent = '-';
                } else {
                    statusBadge.className = 'badge bg-danger';
                    statusBadge.textContent = '失败';
                    errorCell.textContent = result.error || '未知错误';
                }
            });

            // 显示总体操作结果
            const successCount = data.results.filter(r => r.success).length;
            const failCount = data.results.length - successCount;
            const summaryDiv = document.getElementById('batchDebugScriptSummary');
            const summaryText = document.getElementById('batchDebugScriptSummaryText');
            
            if (failCount === 0) {
                summaryDiv.className = 'alert alert-success mb-3';
                summaryText.textContent = `全部启动成功 (${successCount}/${data.results.length})`;
            } else {
                summaryDiv.className = 'alert alert-warning mb-3';
                summaryText.textContent = `部分启动成功 (${successCount}/${data.results.length})，${failCount}个设备启动失败`;
            }
            summaryDiv.style.display = 'block';
        } else {
            // 显示错误信息
            const summaryDiv = document.getElementById('batchDebugScriptSummary');
            const summaryText = document.getElementById('batchDebugScriptSummaryText');
            summaryDiv.className = 'alert alert-danger mb-3';
            summaryText.textContent = `启动失败: ${data.error || '未知错误'}`;
            summaryDiv.style.display = 'block';
        }
    })
    .catch(error => {
        console.error('批量启动调试脚本失败:', error);
        const summaryDiv = document.getElementById('batchDebugScriptSummary');
        const summaryText = document.getElementById('batchDebugScriptSummaryText');
        summaryDiv.className = 'alert alert-danger mb-3';
        summaryText.textContent = '请求失败，请重试';
        summaryDiv.style.display = 'block';
    });
}

// 停止批量调试脚本
function stopBatchDebugScript() {
    const selectedDevices = Array.from(document.querySelectorAll('.device-checkbox:checked')).map(checkbox => {
        const deviceRow = checkbox.closest('tr');
        return {
            id: checkbox.value,
            deviceId: deviceRow.querySelector('td:nth-child(2)').textContent.trim(),
            remark: deviceRow.querySelector('td:nth-child(3)').textContent.trim()
        };
    });

    // 为每个设备创建记录行
    const resultTable = document.getElementById('batchDebugScriptRecords');
    resultTable.innerHTML = '';
    selectedDevices.forEach(device => {
        const recordRow = document.createElement('tr');
        recordRow.innerHTML = `
            <td>${device.deviceId}</td>
            <td>${device.remark}</td>
            <td>停止脚本</td>
            <td><span class="badge bg-secondary">处理中...</span></td>
            <td>-</td>
        `;
        resultTable.appendChild(recordRow);
    });

    // 发送批量停止请求
    fetch('/debug_script/api/devices/batch_stop_debug_script', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            device_ids: selectedDevices.map(d => parseInt(d.id))
        }),
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // 更新每个设备的操作结果
            data.results.forEach((result, index) => {
                const recordRow = resultTable.children[index];
                const statusBadge = recordRow.querySelector('.badge');
                const errorCell = recordRow.querySelector('td:last-child');

                if (result.success) {
                    statusBadge.className = 'badge bg-success';
                    statusBadge.textContent = '成功';
                    errorCell.textContent = '-';
                } else {
                    statusBadge.className = 'badge bg-danger';
                    statusBadge.textContent = '失败';
                    errorCell.textContent = result.error || '未知错误';
                }
            });

            // 显示总体操作结果
            const successCount = data.results.filter(r => r.success).length;
            const failCount = data.results.length - successCount;
            const summaryDiv = document.getElementById('batchDebugScriptSummary');
            const summaryText = document.getElementById('batchDebugScriptSummaryText');
            
            if (failCount === 0) {
                summaryDiv.className = 'alert alert-success mb-3';
                summaryText.textContent = `全部停止成功 (${successCount}/${data.results.length})`;
            } else {
                summaryDiv.className = 'alert alert-warning mb-3';
                summaryText.textContent = `部分停止成功 (${successCount}/${data.results.length})，${failCount}个设备停止失败`;
            }
            summaryDiv.style.display = 'block';
        } else {
            // 显示错误信息
            const summaryDiv = document.getElementById('batchDebugScriptSummary');
            const summaryText = document.getElementById('batchDebugScriptSummaryText');
            summaryDiv.className = 'alert alert-danger mb-3';
            summaryText.textContent = `停止失败: ${data.error || '未知错误'}`;
            summaryDiv.style.display = 'block';
        }
    })
    .catch(error => {
        console.error('批量停止调试脚本失败:', error);
        const summaryDiv = document.getElementById('batchDebugScriptSummary');
        const summaryText = document.getElementById('batchDebugScriptSummaryText');
        summaryDiv.className = 'alert alert-danger mb-3';
        summaryText.textContent = '请求失败，请重试';
        summaryDiv.style.display = 'block';
    });
}
</script>
{% endblock %}