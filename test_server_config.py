#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
设备服务器配置功能测试脚本
用于验证新实现的服务器配置功能
"""

import requests
import json
import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_server_config_api():
    """测试设备服务器配置API功能"""
    base_url = "http://localhost:5000"
    
    print("开始测试设备服务器配置API...")
    
    # 测试获取服务器产品配置API
    print("\n1. 测试获取服务器产品配置API")
    try:
        response = requests.get(f"{base_url}/api/server-config/products")
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                server_types = data.get('server_types', {})
                products = data.get('products', {})
                print(f"   ✓ 服务器产品配置获取成功:")
                print(f"     - 服务器类型: {list(server_types.keys())}")
                print(f"     - 产品数量: 阿里云={len(products.get('alicloud', []))}, EMQX={len(products.get('emqx', []))}")
                
                # 显示产品详情
                for server_type, product_list in products.items():
                    print(f"     - {server_type}产品:")
                    for product in product_list:
                        print(f"       * {product['name']} ({product['product_key']})")
            else:
                print(f"   ✗ 服务器产品配置获取失败: {data.get('error', '未知错误')}")
        else:
            print(f"   ✗ 服务器产品配置API请求失败: HTTP {response.status_code}")
    except Exception as e:
        print(f"   ✗ 服务器产品配置API测试异常: {e}")
    
    # 测试获取可用目标产品API
    print("\n2. 测试获取可用目标产品API")
    test_product_keys = ["hs7eigK8Xvl", "wxd48e69e833621cfd", "wx227137014f90cf30"]
    
    for product_key in test_product_keys:
        try:
            response = requests.get(f"{base_url}/api/server-config/available-targets/{product_key}")
            if response.status_code == 200:
                data = response.json()
                if data.get('success'):
                    current_server_type = data.get('current_server_type')
                    available_targets = data.get('available_targets', [])
                    print(f"   ✓ 产品 {product_key} (类型: {current_server_type}):")
                    print(f"     - 可用目标数量: {len(available_targets)}")
                    for target in available_targets:
                        print(f"       * {target['name']} ({target['product_key']}) - {target.get('migration_type', 'unknown')}")
                else:
                    print(f"   ✗ 产品 {product_key} 目标获取失败: {data.get('error', '未知错误')}")
            else:
                print(f"   ✗ 产品 {product_key} 目标API请求失败: HTTP {response.status_code}")
        except Exception as e:
            print(f"   ✗ 产品 {product_key} 目标API测试异常: {e}")
    
    # 测试服务器配置服务模块
    print("\n3. 测试服务器配置服务模块")
    try:
        from services.server_config_service import server_config_service
        
        # 测试服务器类型检测
        test_cases = [
            ("hs7eigK8Xvl", "alicloud"),
            ("wxd48e69e833621cfd", "emqx"),
            ("wx227137014f90cf30", "emqx"),
            ("unknown_key", "unknown")
        ]
        
        print("   测试服务器类型检测:")
        for product_key, expected_type in test_cases:
            detected_type = server_config_service.detect_server_type(product_key)
            status = "✓" if detected_type == expected_type else "✗"
            print(f"     {status} {product_key} -> {detected_type} (期望: {expected_type})")
        
        # 测试配置验证
        print("   测试配置验证:")
        validation_cases = [
            ("hs7eigK8Xvl", "wxd48e69e833621cfd", True),  # 阿里云到EMQX
            ("wxd48e69e833621cfd", "hs7eigK8Xvl", True),   # EMQX到阿里云
            ("wxd48e69e833621cfd", "wx227137014f90cf30", True),  # EMQX内部切换
            ("hs7eigK8Xvl", "hs7eigK8Xvl", False),  # 相同产品
            ("unknown_key", "hs7eigK8Xvl", False),  # 未知源产品
        ]
        
        for source, target, should_pass in validation_cases:
            is_valid, message = server_config_service.validate_migration(source, target)
            status = "✓" if is_valid == should_pass else "✗"
            print(f"     {status} {source} -> {target}: {message}")
            
    except ImportError as e:
        print(f"   ✗ 服务器配置服务模块导入失败: {e}")
    except Exception as e:
        print(f"   ✗ 服务器配置服务模块测试异常: {e}")
    
    # 测试设备服务器配置管理器
    print("\n4. 测试设备服务器配置管理器")
    try:
        from services.device_server_config import device_server_config_manager
        
        print("   ✓ 设备服务器配置管理器导入成功")
        print("   注意: 实际配置测试需要IoT客户端环境，此处仅测试模块加载")
        
        # 测试配置方法存在性
        methods = ['update_device_server_config', 'config_alicloud_to_emqx', 'config_emqx_to_alicloud', 'config_emqx_product_key']
        for method in methods:
            if hasattr(device_server_config_manager, method):
                print(f"   ✓ 方法 {method} 存在")
            else:
                print(f"   ✗ 方法 {method} 不存在")
                
    except ImportError as e:
        print(f"   ✗ 设备服务器配置管理器导入失败: {e}")
        print("   注意: 这可能是因为缺少IoT客户端依赖，这是正常的")
    except Exception as e:
        print(f"   ✗ 设备服务器配置管理器测试异常: {e}")
    
    print("\n测试完成!")
    print("\n注意事项:")
    print("1. 实际的设备配置功能需要IoT客户端环境支持")
    print("2. 建议在有设备连接的环境中进行完整功能测试")
    print("3. 配置操作会重启设备，请在测试环境中谨慎操作")

if __name__ == "__main__":
    test_server_config_api()
